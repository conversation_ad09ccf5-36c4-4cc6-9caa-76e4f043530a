# Frontend Restructuring Tasks

## Phase 1: Service Layer and Backend Migration Preparation

- [x] Create PlanService
- [x] Create AdditionalService
- [x] Create AddressService
- [ ] Update backend to provide plan data API endpoint
- [ ] Update backend to provide additionals data API endpoint

## Phase 2: Custom Hooks Implementation

- [x] Create usePlanSelection hook
- [x] Create useCheckoutFlow hook
- [x] Create useAddressValidation hook
- [x] Create usePaymentProcessing hook
- [x] Create useAdditionals hook
- [x] Create unified useCheckout hook for migration

## Phase 3: Context Restructuring

- [x] Create NewCheckoutContext using hooks
- [x] Create CheckoutContextWrapper for gradual migration
- [x] Update App.tsx to use the wrapper

## Phase 4: Component Reorganization

- [x] Create common/CheckoutNavigation
- [x] Create common/StepIndicator
- [x] Create common/FormSection
- [x] Refactor steps components to use new structure
- [x] Create new CheckoutForm component
- [x] Refactor payment status components to use new structure
- [x] Refactor plan components to use new structure

## Phase 5: Gradual Migration

- [x] Create new checkout page using new components
- [x] Add route for new checkout page
- [ ] Migrate components one by one to use the new context
- [ ] Test each component after migration
- [ ] Remove old context when all components are migrated

## Phase 6: Final Cleanup

- [ ] Remove CheckoutContextWrapper
- [ ] Update imports to use new structure
- [ ] Final testing of the entire checkout flow

## Upgrade/Downgrade Implementation

- [x] Update PlanCard component to handle upgrade/downgrade scenarios
  - [x] Add logic to determine if an upgrade is immediate or scheduled
  - [x] Add logic to determine if a downgrade is scheduled
  - [x] Add informational messages about when changes will be applied
  - [x] Update button text and behavior based on upgrade/downgrade type
  - [x] Handle yearly to monthly and monthly to yearly transitions
  - [x] Fix plan level calculation to correctly differentiate between monthly and yearly plans
  - [x] Add support for upgrading the current plan by adding more resources

## Skip Payment Success Modal

- [x] Create SkipPaymentSuccess component for displaying success message when payment is skipped
- [x] Update Index.tsx to conditionally show SkipPaymentSuccess modal when skipPayment is true
- [x] Customize success message to indicate plan was updated without additional payment

## Billing Date Calculation Fix

- [x] Adjust calculateYearlyBillingDate function to handle billing dates correctly:
  - [x] When billingDay is current day or future, return installments starting from current month
  - [x] Only when billingDay is in the past, return installments starting from next month
