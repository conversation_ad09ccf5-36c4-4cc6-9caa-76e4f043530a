import { useAuth } from "@/contexts/auth/useAuth";
import { Additional } from "@/types/additional-types";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { CheckoutNavigation } from "./checkout/CheckoutNavigation";
import { AccountTypeStep } from "./checkout/steps/AccountTypeStep";
import { AdditionalServicesStep } from "./checkout/steps/AdditionalServicesStep";
import { AddressStep } from "./checkout/steps/AddressStep";
import { CompanyInfoStep } from "./checkout/steps/CompanyInfoStep";
import { PaymentStep } from "./checkout/steps/PaymentStep";
import { PersonalInfoStep } from "./checkout/steps/PersonalInfoStep";
import { CheckoutFormData } from "./checkout/types";
import { useCheckout } from "@/contexts/checkout/useCheckout";

interface CheckoutFormProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  onSubmit: () => void;
  onBack: () => void;
  additionals: Additional[];
  onAdditionalsChange: (additionals: Additional[]) => void;
  form: UseFormReturn<CheckoutFormData>;
  setLastStepForm: (step: number) => void;
}

export function CheckoutForm({
  currentStep = 1,
  setCurrentStep,
  onSubmit,
  onBack,
  additionals,
  onAdditionalsChange,
  form,
  setLastStepForm,
}: CheckoutFormProps) {
  const {
    watch,
    formState: { errors },
    setValue,
  } = form;
  const { isAuthenticated } = useAuth();
  const { isCompany, steps } = useCheckout();
  const isNotAcceptTerms = isCompany
    ? watch("acceptTerms") !== true && currentStep === 5
    : watch("acceptTerms") !== true && currentStep === 4;

  const cardIsIncomplete =
    watch("cardId") === "" || watch("cardId") === undefined;
  const nextStep = async () => {
    // Validação do formulário
    const valid = await form.trigger();
    if (currentStep > 1 && !valid) {
      return;
    }

    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
      setLastStepForm(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setLastStepForm(currentStep - 1);
    } else {
      onBack();
    }
  };

  const renderStepContent = () => {
    const title = steps[currentStep - 1];
    if (isCompany) {
      switch (currentStep) {
        case 1:
          return (
            <AccountTypeStep
              title={title}
              isCompany={isCompany}
              onTypeChange={(value) => setValue("isCompany", value)}
              disabled={isAuthenticated}
            />
          );
        case 2:
          return (
            <CompanyInfoStep
              title={title}
              register={form.register}
              errors={errors}
            />
          );
        case 3:
          return (
            <PersonalInfoStep
              title={title}
              isCompany={isCompany}
              watch={watch}
              register={form.register}
              errors={errors}
            />
          );
        case 4:
          return <AddressStep title={title} />;
        case 5:
          return (
            <AdditionalServicesStep
              title={title}
              register={form.register}
              errors={errors}
              additionals={additionals}
              onAdditionalsChange={onAdditionalsChange}
            />
          );
        case 6:
          return (
            <PaymentStep
              isCompany={isCompany}
              register={form.register}
              errors={errors}
              additionals={additionals}
              onAdditionalsChange={onAdditionalsChange}
            />
          );
        default:
          return null;
      }
    } else {
      switch (currentStep) {
        case 1:
          return (
            <AccountTypeStep
              title={title}
              isCompany={isCompany}
              onTypeChange={(value) => setValue("isCompany", value)}
              disabled={isAuthenticated}
            />
          );
        case 2:
          return (
            <PersonalInfoStep
              title={title}
              isCompany={false}
              watch={watch}
              register={form.register}
              errors={errors}
            />
          );
        case 3:
          return <AddressStep title={title} />;
        case 4:
          return (
            <AdditionalServicesStep
              title={title}
              register={form.register}
              errors={errors}
              additionals={additionals}
              onAdditionalsChange={onAdditionalsChange}
            />
          );
        case 5:
          return (
            <PaymentStep
              isCompany={isCompany}
              register={form.register}
              errors={errors}
              additionals={additionals}
              onAdditionalsChange={onAdditionalsChange}
            />
          );
        default:
          return null;
      }
    }
  };

  return (
    <FormProvider {...form}>
      <div className="space-y-8 mx-auto">
        {renderStepContent()}

        <CheckoutNavigation
          currentStep={currentStep}
          totalSteps={steps.length}
          onNext={nextStep}
          onPrevious={previousStep}
          onSubmit={onSubmit}
          isNextDisabled={
            (currentStep === 1 && isCompany === null) || isNotAcceptTerms
          }
        />
      </div>
    </FormProvider>
  );
}
