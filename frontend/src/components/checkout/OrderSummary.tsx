import { installments } from '@/config/payment';
import { useCheckout } from '@/contexts/checkout/useCheckout';
import { formatCurrency, formatCurrencyToShow } from '@/lib/utils';
import { formatDate } from 'date-fns';
import { useAuth } from '@/contexts/auth/useAuth';
import { t } from '@/lib/translations.helper';
import { getBasePrice, getCurrentPlanOnMonthly, getTotalPrice, isPartialUpgrading } from '@/lib/plan.utils';

export function OrderSummary() {
  const { subscription } = useAuth();
  const {
    selectedPlan,
    additionalsCalculated,
    total,
    subtotal,
    discount,
    isUpgrade,
    isDowngrade,
    upgradeCredit,
    currentPlan,
    isCurrentYearly,
    plans,
  } =
    useCheckout();

  const { isYearly } = selectedPlan;

  const customFeatures = selectedPlan.customFeatures.map((feature) => {
    return {
      ...feature,
      additionals: feature.quantity - feature.included,
      totalPrice:
        (isYearly ? feature.yearlyPrice * 12 : feature.monthlyPrice) *
        (feature.quantity - feature.included),
    };
  });

  const selectedPlanInMonthly = getCurrentPlanOnMonthly(plans, selectedPlan);
  const maxInstallment = Math.max(...installments);
  const planPrice = getBasePrice(selectedPlan);
  const monthlyPrice = getTotalPrice(selectedPlanInMonthly) * 12;

  const firstAmount = (isYearly ? subtotal / maxInstallment : subtotal) - (upgradeCredit || 0);
  const restAmount = isYearly ? (subtotal - firstAmount) / (maxInstallment - 1) : 0;

  const changingBillingCycle = !!currentPlan && isCurrentYearly !== isYearly;
  const isPartialUpgrade = isPartialUpgrading(currentPlan, selectedPlan)

  return (
    <div className="rounded-lg border p-4 space-y-4">
      <div className="space-y-4 text-sm">
        <div className="flex justify-between text-sm">
          <span>Item</span>
          <span>
            Valor
            {' '}
            <span className="text-xs">
              ({isYearly ? 'ano' : 'mês'})
            </span>
          </span>
        </div>
        {(isUpgrade || isDowngrade) && (
          <span className="text-xs text-muted-foreground font-medium">
            Migrando de {currentPlan.name} {t(`interval.${isCurrentYearly ? 'yearly' : 'monthly'}`)}
            {' para '}
          </span>
        )}
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span className="font-medium">{selectedPlan.name} {t(`interval.${isYearly ? 'yearly' : 'monthly'}`)} </span>
            <span className="text-xs font-normal text-green-600">
              {selectedPlan.leadsCount.toLocaleString()} leads
            </span>
          </div>
          <div className="flex flex-col">
            {isYearly && !isUpgrade && !isPartialUpgrade && selectedPlan.discount > 0 && (
              <span className="line-through text-muted-foreground text-xs">
                {formatCurrency(monthlyPrice)}
              </span>
            )}
            <span>{formatCurrency(planPrice)}</span>
          </div>
        </div>

        {customFeatures
          .filter((f) => f.additionals > 0)
          .map((feature) => (
            <div key={feature.id} className="flex justify-between text-sm">
              <div className="flex flex-col">
                <span className="font-medium text-xs">
                  {feature.quantity} {feature.name}
                </span>
                <span className="text-xs font-normal text-green-600">
                  {feature.included} incluídos + {feature.additionals} extras
                </span>
              </div>
              <span>{formatCurrency(feature.totalPrice)}</span>
            </div>
          ))}

        {additionalsCalculated
          .filter((c) => c.selected)
          .map((item) => (
            <div key={item.id} className="flex justify-between text-sm">
              <span>{item.name}</span>
              <span>{formatCurrency(item.price)}</span>
            </div>
          ))}

        {(isUpgrade || isDowngrade || isPartialUpgrade) && (
          <div className="flex justify-between items-center border-t pt-4">
            <div className="flex-1 ">Subtotal</div>
            <div className="text-right">
              <div className="flex flex-col">
                <p className="relative text-md text-green-600 font-medium">
                  {formatCurrency(subtotal)}
                </p>
              </div>
            </div>
          </div>
        )}

        {discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto</span>
            <span>-{formatCurrency(discount)}</span>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto no upgrade</span>
            <span>-{formatCurrency(upgradeCredit)}</span>
          </div>
        )}

        <div className="border-b" />

        <div className="flex justify-between items-center">
          <div className="flex-1 text-lg bg-clip-text font-medium">Total</div>
          <div className="text-right">
            {isYearly && !isUpgrade && !isPartialUpgrade && selectedPlan.discount > 0 && (
              <span className="line-through text-muted-foreground text-xs">
                {formatCurrency(monthlyPrice)}
              </span>
            )}
            {!upgradeCredit && isYearly && <div className="flex flex-col">
              <p className="relative text-md text-green-600 font-medium">
                {maxInstallment > 1 ? `até ${maxInstallment}x ` : ''}
                {formatCurrency(total / Number(maxInstallment))}
              </p>
              {isYearly && Number(maxInstallment) > 1 && (
                <span className="text-xs font-normal text-muted-foreground">
                  ou {formatCurrency(total)} a vista
                </span>
              )}
            </div>}
            {upgradeCredit > 0 && isYearly && (
              <div className="flex flex-col">
                {maxInstallment > 1 && (
                  <>
                    <p className="relative text-md text-green-600 font-medium">
                      {formatCurrency(total)}

                      <div className="text-xs font-normal text-muted-foreground">
                        {isYearly ? `em até ${maxInstallment}x ` : ''}
                        {isYearly ? 'com entrada de ' : ''}
                      </div>
                      {formatCurrency(firstAmount)}
                      <div className="text-xs font-normal text-muted-foreground">
                        {isYearly ? `+ ${maxInstallment - 1}x ${formatCurrency(restAmount)}` : ''}
                      </div>
                    </p>
                  </>
                )}
                {maxInstallment === 1 && (
                  <p className="relative text-md text-green-600 font-medium">
                    {isYearly ? `até ${maxInstallment}x ` : ''}
                    {isYearly ? formatCurrency(total / Number(maxInstallment)) : formatCurrency(total)}
                    {isYearly ? '' : '/mês'}
                  </p>
                )}
              </div>
            )}
            {
              !isYearly && (
                <div className="flex flex-col">
                  <p className="relative text-md text-green-600 font-medium">
                    {formatCurrency(total)}
                  </p>
                </div>
              )
            }
          </div>
        </div>

        {changingBillingCycle && (
          <div className="flex justify-center text-green-600">
            <p className="text-xs text-amber-600 font-medium mt-1">
              * será aplicado em {formatDate(new Date(subscription.nextBillingDate), 'dd/MM/yyyy')}
            </p>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-center text-green-600">
            <p className="text-xs text-amber-600 font-medium mt-1">
              * a partir de {formatDate(new Date(subscription.nextBillingDate), 'dd/MM/yyyy')}
              {' - '}
              {formatCurrencyToShow(planPrice, isYearly)}
              /mês
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
