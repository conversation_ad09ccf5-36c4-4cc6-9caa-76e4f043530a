import React from "react";
import { Card } from "../ui/card";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { AlertCircle } from "lucide-react";

// Define o formato dos itens que serão exibidos
export interface InfoItem {
  label: string;
  value: React.ReactNode | string;
  append?: React.ReactNode;
}

// Define as props do componente, incluindo o título e os itens
export interface InfoCardProps {
  title: string;
  items: InfoItem[];
  scheduledAction?: string;
}

// Componente genérico que exibe um Card com título e lista de itens
export const InfoCard: React.FC<InfoCardProps> = ({ title, items, scheduledAction }) => {
  return (
    <Card className="p-6">
      <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">{title}</h2>
      <div
        className={`grid grid-cols-${items.length > 1 ? "2" : "1"} gap-4 mb-6`}
      >
        {items.map((item, index) => (
          <div key={index}>
            <p className="text-sm text-muted-foreground">{item.label}</p>
            <p className="font-medium">{item.value}{item.append}</p>
          </div>
        ))}
      </div>
      {scheduledAction === "cancel" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Cancelamento Agendado</AlertTitle>
          <AlertDescription>
            Assinatura será cancelada no fim do ciclo atual.
          </AlertDescription>
        </Alert>
      )}
    </Card>
  );
};

export default InfoCard;
