import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { formatCurrency } from "@/lib/utils";
import { CreditCard, Landmark, QrCode, ChevronLeft, ChevronRight } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { FieldErrors, useFormContext, UseFormRegister, useWatch } from "react-hook-form";
import { InputMessageError } from "../InputMessageError";
import { PlanExtras } from "../plan/PlanExtras";
import { PromoCodeInput } from "../PromoCodeInput";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { GradientTitle } from "../ui/gradient-title";
import { Switch } from "../ui/switch";
import { AddressConfig } from "./AddressConfig";
import { OrderSummary } from "./OrderSummary";
import { BankSlipFields } from "./payment/BankSlipFields";
import { CreditCardFields } from "./payment/CreditCardFields";
import { PixFields } from "./payment/PixFields";
import { CheckoutFormData, PaymentMethod } from "./types";
import { Card } from "../ui/card";
import { subscriptionService } from "@/services/subscription";
import { useAuth } from "@/contexts/auth/useAuth";
import { Account } from "@/types/account";
import { useToast } from "@/hooks/use-toast";
import { ToastAction } from "../ui/toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { installments, paymentDays } from "@/config/payment";

interface PaymentFieldsProps {
  isCompany: boolean;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

// Interface for saved credit cards
interface SavedCreditCard {
  id: string;
  lastFourDigits: string;
  brand: string;
  expiryDate: string;
  holderName: string;
}
export function PaymentFields({ isCompany, register, errors }: PaymentFieldsProps) {
  const { toast } = useToast();
  const {
    selectedPlan,
    isYearly,
    setSelectedPlan,
    switchToYearlyPlan,
    selectedPlanInYearly,
    form,
    total
  } = useCheckout();
  const billingDay = form.watch('billingDay')
  const installment = form.watch('installments')

  const { account = {} as Account, subscription } = useAuth();
  const { control, setValue } = useFormContext();

  const [isCustomizationModalOpen, setIsCustomizationModalOpen] = useState(false);

  const [savedCreditCards, setSavedCreditCards] = useState<SavedCreditCard[]>([]);
  const [isLoadingCards, setIsLoadingCards] = useState(false);

  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);

  const paymentMethod = useWatch({
    name: "paymentMethod",
    control,
  });

  // Fetch customer cards when component mounts and user is available
  useEffect(() => {
    const fetchCustomerCards = async () => {
      if (account) {
        setIsLoadingCards(true);
        try {
          const cards = await subscriptionService.getCustomerCards();
          setSavedCreditCards(cards);
          setIsLoadingCards(false);
        } catch (error) {
          console.error("Failed to fetch customer cards:", error);
        } finally {
          setIsLoadingCards(false);
        }
      }
    };
    fetchCustomerCards();
  }, [account]);

  const handlePromoCode = (discount: number) => {
    setValue("discount", discount);
  };

  const handleCardSelection = (card: SavedCreditCard, cancel?: boolean) => {
    console.log("Selected card:", card, "as", selectedCardId);
    if (selectedCardId === card.id || cancel === true) {
      console.log("entrei no if");
      setSelectedCardId(null);
      setValue("cardId", "");
      setValue("cardExpiry", "");
      // setValue("newCard", f);
    } else {
      console.log("entrei no else");
      setSelectedCardId(card.id);
      setShowNewCardForm(false);
      setValue("cardId", card.id);
      setValue("cardExpiry", card.expiryDate);
      setValue("newCard", false);

      toast({
        title: "Cartão selecionado",
        description: `Cartão com final ${card.lastFourDigits} selecionado com sucesso.`,
        action: (
          <ToastAction altText="Desfazer ação" onClick={() => handleCardSelection(card, true)}>
            Desfazer
          </ToastAction>
        ),
      });
    }
  };

  const handleNewCardClick = () => {
    setSelectedCardId(null);
    setShowNewCardForm(true);
    setValue("newCard", true);
  };

  const scrollCarousel = (direction: "left" | "right") => {
    if (carouselRef.current) {
      const scrollAmount = 240; // Card width + gap
      const currentScroll = carouselRef.current.scrollLeft;
      carouselRef.current.scrollTo({
        left: direction === "left" ? currentScroll - scrollAmount : currentScroll + scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className="grid gap-4 lg:grid-cols-[2fr_1fr]">
      <div className="space-y-4">
        <GradientTitle className="text-1xl">Método de Pagamento</GradientTitle>
        <RadioGroup
          defaultValue={paymentMethod}
          className="flex gap-4"
          onValueChange={(value) => {
            if (value) {
              setValue("paymentMethod", value);
            }
          }}
        >
          <div className="flex-1">
            <RadioGroupItem
              value={PaymentMethod.CREDIT_CARD}
              id={PaymentMethod.CREDIT_CARD}
              className="peer sr-only"
            />
            <Label
              htmlFor={PaymentMethod.CREDIT_CARD}
              className={`flex flex-col items-center justify-between rounded-md border-2 border-muted bg-background p-4 hover:border-black hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary transition-all duration-200 ${paymentMethod === PaymentMethod.CREDIT_CARD ? "shadow-sm" : ""
                }`}
            >
              <CreditCard className="mb-3 h-6 w-6" />
              <span className="text-sm font-medium">Cartão de Crédito</span>
            </Label>
          </div>

          <div className="flex-1">
            <RadioGroupItem
              value={PaymentMethod.PIX}
              id={PaymentMethod.PIX}
              className="peer sr-only"
            />
            <Label
              htmlFor={PaymentMethod.PIX}
              className={`flex flex-col items-center justify-between rounded-md border-2 p-4 hover:border-black hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary transition-all duration-200 ${paymentMethod === PaymentMethod.PIX
                ? "border-primary bg-accent/10 shadow-sm transform scale-105"
                : "border-muted bg-background"
                }`}
            >
              <QrCode
                className={`mb-3 h-7 w-7 ${paymentMethod === PaymentMethod.PIX ? "text-primary" : ""
                  }`}
              />
              <span className="text-sm font-medium">PIX</span>
            </Label>
          </div>

          {isCompany && (
            <div className="flex-1">
              <RadioGroupItem
                value={PaymentMethod.BOLETO}
                id={PaymentMethod.BOLETO}
                className="peer sr-only"
              />
              <Label
                htmlFor={PaymentMethod.BOLETO}
                className={`flex flex-col items-center justify-between rounded-md border-2 border-muted bg-background p-4 hover:border-black hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary transition-all duration-200 ${paymentMethod === PaymentMethod.BOLETO ? "shadow-sm" : ""
                  }`}
              >
                <Landmark className="mb-3 h-6 w-6" />
                <span className="text-sm font-medium">Boleto Bancário</span>
              </Label>
            </div>
          )}
        </RadioGroup>
        <InputMessageError error={errors.paymentMethod?.message} />

        {paymentMethod === PaymentMethod.CREDIT_CARD && (
          <div className="space-y-4">
            <input type="hidden" {...register("cardId")} />
            {/* Saved Credit Cards Carousel */}
            {isLoadingCards ? (
              <div className="py-4 text-center text-sm text-muted-foreground">
                Carregando cartões salvos...
              </div>
            ) : savedCreditCards.length > 0 ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Cartões Salvos</h3>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                      onClick={() => scrollCarousel("left")}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                      onClick={() => scrollCarousel("right")}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="relative max-w-[480px] overflow-hidden">
                  <div
                    ref={carouselRef}
                    className="flex gap-3 overflow-x-hidden scroll-smooth pb-2 px-1"
                    style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
                  >
                    {savedCreditCards.map((card) => (
                      <Card
                        key={card.id}
                        className={`p-4 min-w-[220px] flex-shrink-0 cursor-pointer transition-all ${selectedCardId === card.id
                          ? "border-primary border-2 shadow-md"
                          : "border hover:border-gray-400"
                          }`}
                        onClick={() => handleCardSelection(card, false)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="font-medium capitalize">{card.brand}</div>
                          <Checkbox
                            className="w-4 h-4"
                            checked={selectedCardId === card.id}
                            onCheckedChange={() => handleCardSelection(card, false)}
                          />
                        </div>
                        <div className="text-sm text-muted-foreground mb-1">
                          •••• •••• •••• {card.lastFourDigits}
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="text-xs text-muted-foreground">
                            Expira em {card.expiryDate}
                          </div>
                        </div>
                      </Card>
                    ))}

                    <Card
                      className="p-4 min-w-[220px] flex-shrink-0 cursor-pointer border-dashed border-2 flex items-center justify-center hover:border-primary/50"
                      onClick={handleNewCardClick}
                    >
                      <div className="text-center">
                        <CreditCard className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                        <div className="text-sm font-medium">Novo Cartão</div>
                      </div>
                    </Card>
                  </div>
                </div>
              </div>
            ) : (
              savedCreditCards.length === 0 && (
                <CreditCardFields register={register} errors={errors} />
              )
            )}

            {showNewCardForm && <CreditCardFields register={register} errors={errors} />}
            <AddressConfig />
          </div>
        )}

        {paymentMethod === PaymentMethod.PIX && <PixFields register={register} errors={errors} />}

        {paymentMethod === PaymentMethod.BOLETO && <BankSlipFields register={register} errors={errors} />}

        {isYearly && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="installments">Parcelas</Label>
              <Select
                defaultValue={installments[0].toString()}
                onValueChange={(value) => setValue("installments", value)}
                disabled={!isYearly}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o número de parcelas" />
                </SelectTrigger>
                <SelectContent>
                  {installments.map((installment) => (
                    <SelectItem key={installment} value={installment.toString()}>
                      {installment}x {formatCurrency(total / installment)} sem juros
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <InputMessageError error={errors.installments?.message} />
            </div>
            {Number(installment) > 1 && (
              <div>
                <Label htmlFor="billingDay">Qual o melhor dia para vencimento?</Label>
                <Select
                  defaultValue={paymentDays[0].toString()}
                  value={billingDay.toString()}
                  onValueChange={(value) => setValue("billingDay", value)}
                  disabled={!!subscription?.billingDay}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a data de vencimento" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentDays.map((day) => (
                      <SelectItem key={day} value={day.toString()}>Todo dia {day}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InputMessageError error={errors.installments?.message} />
              </div>
            )}
          </div>
        )}

      </div>

      <div className="space-y-4">
        <div className="flex justify-between">
          <div className="flex-1 text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500">
            Resumo do Pedido
          </div>

          {isYearly && selectedPlan.discount > 0 && (
            <div className="p-3 flex items-center gap-1 bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600 px-3 py-1 rounded-full text-white text-xs font-medium shadow-md h-fit">
              🔥&nbsp; {selectedPlan.discountPercentage.toFixed(0)}% no anual
            </div>
          )}
        </div>

        {!isYearly && (
          <div className="flex justify-between">
            <GradientTitle className="text-1xl">Quero o plano anual</GradientTitle>

            <Switch checked={isYearly} onCheckedChange={switchToYearlyPlan} />
          </div>
        )}

        {!isYearly && selectedPlanInYearly?.discount > 0 && (
          <span className="text-green-600 font-medium text-sm">
            🔥 Poupe {formatCurrency(selectedPlan.discount)} no plano anual!
          </span>
        )}
        <OrderSummary />

        <Button
          className="w-full rounded-full text-[#0071e2] hover:text-[#0071e2]/90 hover:bg-[#0071e2]/10 text-base font-medium group relative overflow-hidden transition-all duration-300"
          variant="outline"
          onClick={(e) => {
            e.stopPropagation();
            setIsCustomizationModalOpen(true);
          }}
        >
          <span className="relative z-10 flex items-center justify-center gap-2">
            Personalizar Plano
          </span>
        </Button>

        <PromoCodeInput onApply={handlePromoCode} />
      </div>

      {selectedPlan && (
        <PlanExtras
          isYearly={isYearly}
          isOpen={isCustomizationModalOpen}
          onClose={() => setIsCustomizationModalOpen(false)}
          plan={selectedPlan}
          setPlan={setSelectedPlan}
        />
      )}
    </div>
  );
}
