import { Subscription } from "@/models/Subscription";
import { formatDate } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion } from "framer-motion";
import { Check } from "lucide-react";

const itemAnimation = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const formatCycleDates = (cycle: { start_at: string; end_at: string }) => {
  const startDateFormatted = formatDate(cycle.start_at, "dd/MM/yyyy", {
    locale: ptBR,
  });
  const endDateFormatted = formatDate(cycle.end_at, "dd/MM/yyyy", {
    locale: ptBR,
  });
  return `${startDateFormatted} - ${endDateFormatted}`;
};

export const SubscriptionDetails = ({
  subscription,
}: {
  subscription: Subscription;
}) => {
  return (
    <motion.div
      variants={itemAnimation}
      className="bg-gradient-to-br from-slate-50 to-slate-100/50 dark:from-gray-900 dark:to-gray-800 p-4 rounded-2xl space-y-4 border border-slate-200/50"
    >
      <div className="flex items-center justify-between">
        <span className="text-slate-600 dark:text-gray-100">Período</span>
        <span
          className="font-mono font-semibold text-slate-900 dark:text-gray-100 bg-white dark:bg-gray-900 px-4 py-1.5 rounded-lg border border-slate-200 shadow-sm"
          title="Período de vigência da assinatura"
        >
          {formatCycleDates(subscription.currentCycle)}
        </span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-slate-600 dark:text-gray-100">Plano</span>
        <span className="font-semibold text-slate-900 dark:text-gray-100">
          {subscription.planName}
        </span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-slate-600 dark:text-gray-100">Status</span>
        <span className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-[#0071E3]/10 border border-[#0071E3]/20 dark:bg-[#0071E3]/20 dark:border-[#0071E3]/40">
          <span className="text-[#0071E3] font-semibold dark:text-gray-100">
            {subscription.paymentStatus}
          </span>
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 20,
            }}
            className="relative"
          >
            <div className="absolute inset-0 rounded-full bg-[#0071E3]/30 animate-[pulse_2s_infinite]" />
            <div className="relative z-10 bg-[#0071E3] rounded-full p-1">
              <Check className="w-3.5 h-3.5 text-white" strokeWidth={3} />
            </div>
          </motion.div>
        </span>
      </div>
    </motion.div>
  );
};
