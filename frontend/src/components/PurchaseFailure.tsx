import { ActionButtons } from "@/components/payment/failure/ActionButtons";
import { FailureHeader } from "@/components/payment/failure/FailureHeader";
import { FailureIcon } from "@/components/payment/failure/FailureIcon";
import { SupportFooter } from "@/components/payment/SupportFooter";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { motion } from "framer-motion";

interface PurchaseFailureModalProps {
    isOpen: boolean;
    onClose: () => void;
    errorMessage?: string;
}

const PurchaseFailure = ({ isOpen, onClose, errorMessage }: PurchaseFailureModalProps) => {
    const containerAnimation = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                staggerChildren: 0.25
            }
        }
    };

    const itemAnimation = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-2xl p-0 bg-transparent dark:bg-transparent border-none" aria-describedby={undefined}>
                <style>
                    {`
          .dialog-overlay {
            background-color: rgba(255, 255, 255, 0.2) !important;
            backdrop-filter: blur(4px);
          }
          `}
                </style>
                <motion.div
                    variants={containerAnimation}
                    initial="hidden"
                    animate="visible"
                    className="w-full relative z-10 space-y-2"
                >
                    <Card className="p-8 space-y-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-slate-200/50 shadow-2xl">
                        <DialogTitle>

                            <motion.div
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                className="flex justify-center"
                            >
                                <img
                                    src="/qi-plus-brand.png"
                                    alt="QI PLUS Logo"
                                    className="h-16 w-auto px-4 py-2"
                                />
                            </motion.div>
                        </DialogTitle>

                        <div className="text-center space-y-4">
                            <FailureIcon />
                            <FailureHeader
                                itemAnimation={itemAnimation}
                                errorMessage={errorMessage}
                            />
                        </div>

                        <ActionButtons itemAnimation={itemAnimation} />
                        <SupportFooter itemAnimation={itemAnimation} />
                    </Card>
                </motion.div>
            </DialogContent>
        </Dialog>
    );
};

export default PurchaseFailure; 