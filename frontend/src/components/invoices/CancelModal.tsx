import { formatCycleDate } from "@/lib/invoice.utils";

interface CancelModalProps {
  onCancel: () => void;
  onClose: () => void;
  nextBillingDate: string;
}

const CancelModal = ({
  onCancel,
  onClose,
  nextBillingDate,
}: CancelModalProps) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-md max-w-sm w-full">
        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-100">
          Você realmente deseja cancelar sua assinatura?
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          A gente adoraria que você continuasse com a gente. Sabemos o quanto
          essa jornada foi importante até aqui, e seria uma pena te ver partir.
          💔
        </p>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          Se algo não saiu como você esperava ou se tiver qualquer dúvida, fale
          com a gente. Nosso time de suporte está pronto pra te ajudar com o que
          for preciso: Entrar em contato com o suporte.
        </p>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          Mesmo assim, se decidir cancelar agora, sua assinatura continuará
          ativa até {formatCycleDate(nextBillingDate) || ""}.
        </p>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded bg-green-500 hover:bg-green-600 text-white font-semibold transition-colors"
          >
            Manter Assinatura
          </button>
          <button
            onClick={onCancel}
            className="px-4 py-2 rounded bg-red-500 hover:bg-red-600 text-white"
          >
            Sim, Cancelar
          </button>
        </div>
      </div>
    </div>
  );
};

export default CancelModal;
