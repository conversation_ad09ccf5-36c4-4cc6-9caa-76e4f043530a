import { formatDate } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ArrowLeftIcon, ArrowRightIcon, Receipt } from "lucide-react";
import { Invoice } from "@/types/invoice.types";
import { formatCurrency } from "@/lib/utils";
import { formatInterval } from "@/lib/invoice.utils";
import { t } from "@/lib/translations.helper";
import { Card } from "../ui/card";

interface InvoiceTableProps {
  invoices: Invoice[];
  loading: boolean;
  subscriptionInterval: string;
  onOpenBoleto: (invoiceId: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
}

/**
 * Format date to Brazilian format
 */
const formatCycleDate = (date: string): string => {
  if (!date) return "";
  return formatDate(new Date(date), "dd/MM/yyyy", { locale: ptBR });
};

const InvoiceTable = ({
  invoices,
  loading,
  subscriptionInterval,
  onOpenBoleto,
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
}: InvoiceTableProps) => {
  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentInvoices = invoices.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <Card className="p-6 mt-6 tracking-wide">
      <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">
        Histórico de Faturas
      </h2>
      <div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-muted-foreground text-sm uppercase tracking-wide pb-2">
                <th className="pb-2">Valor Total</th>
                <th className="pb-2">Método de Pagamento</th>
                <th className="pb-2">Status</th>
                <th className="pb-2">Vencimento</th>
                <th className="pb-2">Parcelas</th>
                {/* <th className="pb-2 text-center align-middle">Ações</th> */}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <tr key={index} className="animate-pulse">
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      </td>
                      <td className="py-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      </td>
                      {/* <td className="py-4 text-center">
                        <div className="h-4 w-4 bg-gray-200 rounded-full mx-auto"></div>
                      </td> */}
                    </tr>
                  ))
              ) : currentInvoices.length > 0 ? (
                currentInvoices.map((invoice, index) => (
                  <tr key={invoice.id || index} className="border-b">
                    <td className="py-2">
                      {formatCurrency(invoice?.amount)} /
                      {formatInterval(subscriptionInterval)}
                    </td>
                    <td className="py-2">{t(`payment.${invoice.transaction_type}`)}</td>
                    <td className="py-2 capitalize">{t(`invoice.status.${invoice.status}`)}</td>
                    <td className="py-2">{formatCycleDate(invoice.due_at)}</td>
                    <td className="py-2">{invoice.installment}</td>
                    {invoice.transaction_type === "boleto" && (
                      <td className="py-2 text-center align-middle">
                        <button
                          onClick={() => onOpenBoleto(invoice.id)}
                          title="Visualizar Boleto"
                          className="mx-auto text-primary hover:opacity-75 focus:outline-none dark:text-white"
                        >
                          <Receipt size={18} />
                        </button>
                      </td>
                    )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td className="py-4 text-center" colSpan={6}>
                    Nenhum registro de pagamento encontrado
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {invoices.length > itemsPerPage && (
          <div className="flex justify-center items-center mt-6 mb-4">
            <nav className="flex items-center gap-4">
              <button
                onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
                className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1 dark:text-white dark:hover:text-gray-300"
              >
                <ArrowLeftIcon />
              </button>

              <span className="text-sm text-gray-700 dark:text-gray-300">
                {currentPage} / {totalPages}
              </span>

              <button
                onClick={() =>
                  onPageChange(Math.min(currentPage + 1, totalPages))
                }
                disabled={currentPage === totalPages}
                className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1 dark:text-white dark:hover:text-gray-300"
              >
                <ArrowRightIcon />
              </button>
            </nav>
          </div>
        )}
      </div>
    </Card>
  );
};

export default InvoiceTable;
