import { PlanModel } from "@/models/Plan";
import { Plan } from "@/types/plan-types";

// Verifica se os planos são iguais, considerando leads e recursos personalizados
export function isSamePlan(planA: Plan | undefined, planB: Plan | undefined): boolean {
  if (!planA || !planB || planB.uniqueId !== planA.uniqueId) return false;

  return true;
}

export function isPartialUpgrading(currentPlan: Plan | undefined, newPlan: Plan | undefined): boolean {
  if (!currentPlan || !newPlan || newPlan.uniqueId !== currentPlan.uniqueId) return false;

  // Verifica se há mais leads
  if (newPlan.leadsCount > currentPlan.leadsCount) return true;

  // Verifica se há mais recursos personalizados
  const currentTotalFeatures = currentPlan.originalFeatures?.reduce((acc, feature) => acc + feature.quantity, 0);
  const compareTotalFeatures = newPlan.customFeatures?.reduce((acc, feature) => acc + feature.quantity, 0);
  if (compareTotalFeatures < currentTotalFeatures) return true;

  return false;
};

export function getPlanOption(plan: Plan, leadsCount: number) {
  return (
    plan.options.find(
      (option) =>
        leadsCount >= option.contacts_min && leadsCount <= option.contacts_max,
    ) || null
  );
}

/**
 * Get the base price of a plan in cents
 * @param plan Plan object
 * @returns Base price in cents
 */
export function getBasePrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  const option = getPlanOption(plan, plan.leadsCount);

  // Convert to cents
  const basePrice = plan.isYearly
    ? Math.round(option?.yearly_value * 12)
    : Math.round(option?.monthly_value);

  return basePrice;
}

/**
 * Get the price of custom features in cents
 * @param plan Plan object
 * @returns Custom features price in cents
 */
export function getCustomFeaturesPrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  const customFeaturesPrice = plan.customFeatures.reduce((acc, feature) => {
    const quantity = Math.max(feature.included, Math.min(Infinity, feature.quantity));
    const additionals = quantity - feature.included;

    // Convert to cents
    const priceInCents = plan.isYearly
      ? Math.round(feature.yearlyPrice * 12)
      : Math.round(feature.monthlyPrice);

    return acc + priceInCents * additionals;
  }, 0);

  return customFeaturesPrice;
}

/**
 * Get the total price of a plan in cents
 * @param plan Plan object
 * @returns Total price in cents
 */
export function getTotalPrice(plan: Plan | undefined) {
  if (!plan) {
    return 0;
  }

  return getBasePrice(plan) + getCustomFeaturesPrice(plan);
}

export function getCurrentPlanOnYearly(plans: Plan[], currentPlan: Plan | null) {
  if (!currentPlan) return null;

  const currentPlanYearly = plans.find((plan) => plan.id === currentPlan.id && plan.isYearly);
  if (!currentPlanYearly) return currentPlan;

  currentPlanYearly.customFeatures = currentPlan.customFeatures || [];
  currentPlanYearly.originalFeatures = currentPlan.originalFeatures || [];
  currentPlanYearly.leadsCount = currentPlan.leadsCount;
  currentPlanYearly.isYearly = true;
  return currentPlanYearly;
}

export function getCurrentPlanOnMonthly(plans: Plan[], currentPlan: Plan | null) {
  if (!currentPlan) return null;

  const currentPlanMonthly = plans.find((plan) => plan.id === currentPlan.id && !plan.isYearly);
  if (!currentPlanMonthly) return currentPlan;

  const plan = PlanModel.recriate(currentPlanMonthly);

  plan.customFeatures = currentPlan.customFeatures || [];
  plan.originalFeatures = currentPlan.originalFeatures || [];
  plan.leadsCount = currentPlan.leadsCount;
  plan.isYearly = false;
  return plan;
}


/**
 * Calculate discount between monthly and yearly values in cents
 * @param monthlyValueCents Monthly value in cents
 * @param yearlyValueCents Yearly value in cents
 * @returns Discount amount in cents
 */
export function getDiscount(monthlyValueCents: number, yearlyValueCents: number) {
  return (monthlyValueCents - yearlyValueCents);
}

/**
 * Calculate discount percentage between monthly and yearly values
 * @param monthlyValueCents Monthly value in cents
 * @param yearlyValueCents Yearly value in cents
 * @returns Discount percentage
 */
export function getDiscountPercentage(monthlyValueCents: number, yearlyValueCents: number) {
  return ((monthlyValueCents - yearlyValueCents) / monthlyValueCents) * 100;
}
