import { CheckoutFormData } from "@/components/checkout/types";
import { Additional } from "@/types/additional-types";
import { QISubscriptionExented } from "@/types/backend/qiplus.types";
import { PaymentProcessStatus } from "@/types/payment.type";
import { Plan } from "@/types/plan-types";
import { ViaCEPResponse } from "@/types/viacep-types";
import { ReactNode } from "react";
import { UseFormReturn } from "react-hook-form";

export interface CountryCode {
  code: string;
  country: string;
  value: string;
  flag: string;
  mask: {
    mobile: string;
    phone: string;
  };
  pattern: RegExp;
  placeholder: {
    mobile: string;
    phone: string;
  };
}

export interface CheckoutContextData {
  plans: Plan[];
  setPlans: (plans: Plan[]) => void;
  selectedPlan: Plan | null;
  selectedPlanInMonthly: Plan | null;
  selectedPlanInYearly: Plan | null;
  setSelectedPlan: (plan: Plan | null) => void;
  isYearly: boolean;
  switchToYearlyPlan: (isYearly: boolean) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  additionals: Additional[];
  setAdditionals: (additionals: Additional[]) => void;
  handleBack: () => void;
  onLeadsChange: (planId: string, quantity: number) => void;
  moreSavingYearly: Plan;
  paySubscription: () => void;
  countryCodes: CountryCode[];
  selectedCountry: CountryCode;
  setSelectedCountryCode: (country: string) => void;
  form: UseFormReturn<CheckoutFormData>;
  autoCompletedAddress: Record<string, ViaCEPResponse> | null;
  toBilling: boolean;
  setToBilling: (toBilling: boolean) => void;
  lastStepForm: number;
  setLastStepForm: (step: number) => void;
  payment: PaymentProcessStatus;
  setPayment: (payment: PaymentProcessStatus) => void;
  resetCheckout: () => void;
  additionalsCalculated: Additional[];
  total: number;
  subtotal: number;
  discount: number;
  isCompany: boolean;
  steps: string[];
  currentPlan: Plan | null;
  isCurrentYearly: boolean;
  isUpgrade: boolean;
  isDowngrade: boolean;
  upgradeCredit: number | undefined;
  remainingInstallments: number;
  reactivateSubscription: () => Promise<QISubscriptionExented | null>;
}

export interface CheckoutProviderProps {
  children: ReactNode;
}
