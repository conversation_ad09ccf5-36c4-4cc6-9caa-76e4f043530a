import { FloatCenterButton } from '@/components/chat/FloatCenterButton';
import { LiveSupportChat } from '@/components/chat/LiveSupportChat';
import { CheckoutForm } from '@/components/CheckoutForm';
import { CheckoutHeader } from '@/components/CheckoutHeader';
import PixPayments from '@/components/PixPayments';
import { PlanExtras } from '@/components/plan/PlanExtras';
import { PlanCard } from '@/components/PlanCard';
import { PlanCardSkeleton } from '@/components/PlanCardSkeleton';
import { PlanComparison } from '@/components/PlanComparison';
import ProcessingPayment from '@/components/ProcessingPayment';
import PurchaseFailure from '@/components/PurchaseFailure';
import PurchaseSuccess from '@/components/PurchaseSuccess';
import SkipPaymentSuccess from '@/components/SkipPaymentSuccess';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/auth/useAuth';
import { useCheckout } from '@/contexts/checkout/useCheckout';
import { isPartialUpgrading, isSamePlan } from '@/lib/plan.utils';
import { cn } from '@/lib/utils';
import { PaymentStatus } from '@/types/payment.type';
import { useState, useEffect } from 'react';

export default function Index() {
  const {
    selectedPlan,
    setSelectedPlan,
    isYearly,
    currentStep,
    setCurrentStep,
    additionals,
    setAdditionals,
    handleBack,
    onLeadsChange,
    plans,
    moreSavingYearly,
    form,
    setLastStepForm,
    payment,
    setPayment,
    paySubscription,
    currentPlan,
    isCurrentYearly,
    remainingInstallments
  } = useCheckout();

  const { account, loading } = useAuth();
  const [isCustomizationModalOpen, setIsCustomizationModalOpen] = useState(false);
  const [showSkeleton, setShowSkeleton] = useState(true);
  const [cardsReady, setCardsReady] = useState(false);

  // Controla a transição entre skeleton e cards reais
  useEffect(() => {
    if (!loading && plans.length > 0) {
      // Marca os cards como prontos para renderizar
      setCardsReady(true);

      // Mantém o skeleton visível por mais 500ms para garantir que os cards estejam renderizados
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [loading, plans]);

  const continueIsDisabled = () => {
    if (account) {
      return isSamePlan(currentPlan, selectedPlan) && !isPartialUpgrading(currentPlan, selectedPlan);
    }
    return false;
  };

  const yearlyPlans = plans.filter((plan) => plan.isYearly);
  const monthlyPlans = plans.filter((plan) => !plan.isYearly);

  return (
    <div className="min-h-screen mb-[5rem] px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <CheckoutHeader />

        {currentStep === 0 && (
          <>
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold mb-4">Escolha Seu Plano</h1>
              <p className="text-xl text-muted-foreground mb-8">
                Selecione o plano perfeito para suas necessidades
              </p>
            </div>

            {loading || showSkeleton ? (
              <div
                className={cn(
                  'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8',
                  !loading && cardsReady ? 'animate-fade-out' : '',
                )}
              >
                <PlanCardSkeleton />
                <PlanCardSkeleton />
                <PlanCardSkeleton />
              </div>
            ) : (
              <Tabs defaultValue={isYearly ? 'yearly' : 'monthly'} >
                <div className="flex justify-center mb-8">
                  <TabsList>
                    <TabsTrigger value="monthly">Mensal</TabsTrigger>
                    <TabsTrigger value="yearly">
                      Anual&nbsp;
                      <Badge variant="outline" className="ml-2 text-green-500">
                        {moreSavingYearly?.discountPercentage?.toFixed(0)}% OFF
                      </Badge>
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="monthly" className="animate-fade-in duration-500">
                  <div
                    className={cn(
                      'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8',
                      cardsReady ? 'animate-fade-in' : 'opacity-0',
                    )}
                  >
                    {monthlyPlans.map((plan) => (
                      <PlanCard
                        key={plan.uniqueId}
                        plan={plan}
                        currentPlan={currentPlan}
                        isCurrentYearly={isCurrentYearly}
                        selectedPlan={selectedPlan}
                        isYearly={isYearly}
                        onSelect={() => setSelectedPlan(plan)}
                        onLeadsChange={onLeadsChange}
                        remainingInstallments={remainingInstallments}
                        onCustomize={() => {
                          setSelectedPlan(plan);
                          setIsCustomizationModalOpen(true);
                        }}
                      />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="yearly" className="animate-fade-in duration-500">
                  <div
                    className={cn(
                      'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8',
                      cardsReady ? 'animate-fade-in' : 'opacity-0',
                    )}
                  >
                    {yearlyPlans.map((plan) => (
                      <PlanCard
                        key={plan.uniqueId}
                        plan={plan}
                        currentPlan={currentPlan}
                        isCurrentYearly={isCurrentYearly}
                        selectedPlan={selectedPlan}
                        isYearly={isYearly}
                        onSelect={() => setSelectedPlan(plan)}
                        onLeadsChange={onLeadsChange}
                        remainingInstallments={remainingInstallments}
                        onCustomize={() => {
                          setSelectedPlan(plan);
                          setIsCustomizationModalOpen(true);
                        }}
                      />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            )}

            {selectedPlan && (
              <PlanExtras
                isYearly={isYearly}
                isOpen={isCustomizationModalOpen}
                onClose={() => setIsCustomizationModalOpen(false)}
                plan={selectedPlan}
                setPlan={setSelectedPlan}
              />
            )}
            <div
              className={cn('max-w-md mx-auto mt-8 mb-8', loading || showSkeleton ? 'hidden' : 'animate-fade-in')}
            >
              <PlanComparison plans={plans} />
            </div>
          </>
        )}

        {!loading && currentStep > 0 && selectedPlan !== null && (
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-center mb-8">Complete Sua Compra</h2>
            <CheckoutForm
              currentStep={currentStep}
              setCurrentStep={setCurrentStep}
              setLastStepForm={setLastStepForm}
              form={form}
              onSubmit={paySubscription}
              onBack={handleBack}
              additionals={additionals}
              onAdditionalsChange={setAdditionals}
            />
          </div>
        )}
      </div>
      <PurchaseSuccess
        isOpen={payment.status === PaymentStatus.SUCCESS && !payment.processing && !payment.skipPayment}
        onClose={() => setPayment({ ...payment, status: PaymentStatus.STOPPED })}
      />
      <SkipPaymentSuccess
        isOpen={payment.status === PaymentStatus.SUCCESS && !payment.processing && payment.skipPayment}
        onClose={() => setPayment({ ...payment, status: PaymentStatus.STOPPED })}
      />
      <PurchaseFailure
        isOpen={payment.status === PaymentStatus.ERROR && !payment.processing}
        onClose={() => setPayment({ ...payment, status: PaymentStatus.STOPPED })}
        errorMessage={payment.error}
      />
      <PixPayments
        isOpen={payment.status === PaymentStatus.WAITING && !payment.processing}
        payment={payment}
        onClose={() => setPayment({ ...payment, status: PaymentStatus.STOPPED })}
      />
      <ProcessingPayment
        isOpen={payment.status === PaymentStatus.PROCESSING || payment.processing}
        onFinish={() => setPayment({ ...payment, processing: false })}
      />
      {!loading && currentStep === 0 && (
        <FloatCenterButton
          hidden={loading || selectedPlan === null || currentStep > 0}
          title="Continuar"
          onClick={() => !!selectedPlan && setCurrentStep(1)}
          disabled={continueIsDisabled()}
        />)}
      <LiveSupportChat hidden={loading || showSkeleton} />
    </div>
  );
}
