import { Card } from "@/components/ui/card";
import { LiveSupportChat } from "@/components/chat/LiveSupportChat";
import { formatDate, sub } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn, formatCurrency } from "@/lib/utils";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ChevronLeft,
  ChevronRight,
  Receipt,
  RefreshCw,
} from "lucide-react";
import { subscriptionService } from "@/services/subscription";
import { useEffect, useState } from "react";
import InfoCard, { InfoItem } from "@/components/checkout/InfoCard";
import SliderCard from "@/components/InfoSliderCard";
import React from "react";
import { useNavigate } from "react-router-dom";
import { SubscriptionHeader } from "@/components/SubascriptionHeader";
import { useAuth } from "@/contexts/auth/useAuth";
import { Button } from "@/components/ui/button";
import { QISubscriptionExented } from "@/types/backend/qiplus.types";
import { t } from "@/lib/translations.helper";
import PixPayments from "@/components/PixPayments";
import { useCheckout } from "@/contexts/checkout/useCheckout";
interface PaymentHistoryItem {
  isUpgrade: any;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  items: any[];
  paymentMethod: string;
  next_billing_at: string;
  plan: {
    name: string;
  };
  status: string;
  invoiceUrl?: string;
  id: string;
  isCurrent: boolean;
  upgradeTo: string;
}

const ArrowButton = ({
  onClick,
  direction,
}: {
  onClick?: () => void;
  direction: "left" | "right";
}) => (
  <button
    onClick={onClick}
    className={`absolute top-1/2 transform -translate-y-1/2 z-10 ${direction === "left" ? "-left-4" : "-right-4"
      } text-[#0071e2] bg-transparent p-1 hover:opacity-75 dark:text-white`}
  >
    {direction === "left" ? (
      <ChevronLeft size={18} />
    ) : (
      <ChevronRight size={18} />
    )}
  </button>
);

export default function Subscription() {
  const navigate = useNavigate();
  const { loading: isLogging, subscription: currentSubscription } = useAuth();
  const { reactivateSubscription } = useCheckout();

  const [paymentData, setPaymentData] = useState<any>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<
    PaymentHistoryItem[]
  >([]);
  const [subscription, setSubscription] = useState<QISubscriptionExented | null>(null);
  const [increments, setIncrements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5; // Quantidade de registros por página
  // reactivateSubscription
  const scheduledCancel = subscription?.scheduledAction === "cancel";
  const subscriptionCanceled = subscription?.status === "canceled";
  const isLoading = loading || isLogging;

  const handleReactivate = async () => {
    try {
      reactivateSubscription().then((subscription) => {
        console.log("subscription reactivated", subscription);
        if (!subscription) return;
        setSubscription(subscription);
      });
    } catch (error) {
      console.error("Error reactivating subscription:", error);
    }
  };

  useEffect(() => {
    if (isLogging) return;
    async function fetchSubscriptionHistory() {
      try {
        const sub = await subscriptionService.getSubscriptions();
        setSubscription(sub || null);
        setSubscriptionHistory(sub?.subscriptions || []);
        setIncrements(sub?.increments || []);
      } catch (error) {
        console.error("Error fetching subscription data:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchSubscriptionHistory();
  }, [isLogging]);

  const convertName = (name: string) => {
    switch (name) {
      case "funnels_included":
        return "Funils de Vendas";
      case "landing-pages_included":
        return "Landing Pages";
      case "users_included":
        return "Usuários";
      case "shotx-module":
        return "Shotx";
      default:
        return name;
    }
  };

  const planItems = increments.map((item) => ({
    name: convertName(item.name), // Convertendo o id para um nome legível
    value: item.totalPrice,
    quantity: item.quantity,
  }));

  const formatCycleDate = (date: string) => {
    if (!date) return "";
    return formatDate(new Date(date), "dd/MM/yyyy", { locale: ptBR });
  };

  const sliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    nextArrow: <ArrowButton direction="right" />,
    prevArrow: <ArrowButton direction="left" />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  const paySubscription = async () => {
    try {
      setPaymentData(subscription?.invoices[0]);
      console.log("paymentData", paymentData);
    } catch (error) {
      console.error("Error paying subscription:", error);
    }
  };

  let infoCardData: InfoItem[] = [
    {
      label: "Você não tem um plano",
      value: (
        <div className="w-full flex justify-between">
          <span className="text-sm text-[#0071e2]">
            Escolha agora mesmo o plano perfeito para você
          </span>
          <Button onClick={() => navigate("/")}>Escolher Plano</Button>
        </div>
      ),
      append: null
    },
  ];

  if (!!subscription?.next_billing_date) {
    infoCardData = [
      {
        label: "Plano",
        value: subscription?.plan || "Você não tem um plano",
        append: subscription?.interval && <span className="text-xs text-muted-foreground">
          &nbsp;
          ({t(`interval.${subscription?.interval}`)})
        </span>
      },
      {
        label: subscriptionCanceled || scheduledCancel ? "Acesso até" : "Próxima cobrança",
        value: formatCycleDate(subscription?.next_billing_date) || "-",
      },
      {
        label: "Valor",
        value: `${formatCurrency(subscription?.amount || 0)}`,
        append: <span >
          <span className="text-xs text-muted-foreground">
            /{t(`interval.${subscription?.interval}`)}
          </span>
        </span>
      },
      {
        label: "Status",
        value: scheduledCancel ? (
          <button
            className="mt-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center gap-2 px-3 py-1.5 shadow-lg hover:scale-105 transition-transform duration-200 text-sm group"
            title="Reativar Conta"
            onClick={handleReactivate}
          >
            <RefreshCw className="text-white transition-transform duration-200 group-hover:rotate-180 size-4" />
            <span className="text-white">Reativar conta</span>
          </button>
        ) : <span className={cn(
          "text-xs font-medium me-2 px-2.5 py-1 rounded-md", subscription?.status === "active" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300")}>
          {t(`status.${subscription?.status}`) || "-"}
        </span>,
        append: subscription?.status === "pending" ? (
          <Button onClick={paySubscription}>Pagar Agora</Button>
        ) : null
      },
    ];
  }

  // Cálculo de paginação
  const totalPages = Math.ceil(subscriptionHistory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentHistoryItems = subscriptionHistory.slice(startIndex, endIndex);

  const getTotal = (items: any[]) => {
    return items.reduce((acc, item) => acc + item.totalPrice, 0);
  };

  return (
    <div className="min-h-screen mb-[5rem] px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <SubscriptionHeader />

        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Gerenciar Assinatura</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Gerencie sua assinatura atual e veja o histórico de pagamentos
          </p>
        </div>

        <div className="mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
          {isLoading ? (
            <>
              <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
              <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
            </>
          ) : (
            <>
              <InfoCard title="Assinatura" items={infoCardData} scheduledAction={subscription?.scheduledAction} />
              <SliderCard
                title="Itens do Plano"
                sliderSettings={sliderSettings}
                items={planItems}
                renderItem={(item) => (
                  <div className="bg-muted p-4 rounded-lg mx-2">
                    <p className="text-sm text-muted-foreground">{item.name}</p>
                    <p className="text-sm text-muted-foreground mb-2">
                      Quantidade: {item.quantity}
                    </p>
                    <p className="font-medium">{formatCurrency(item.value)}</p>
                  </div>
                )}
              />
            </>
          )}
        </div>

        {/* Histórico de Pagamentos */}
        <Card className="p-6 mt-6 tracking-wide">
          <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">
            Histórico de Assinaturas
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-muted-foreground text-sm uppercase tracking-wide pb-2">
                  <th>Período</th>
                  <th>Plano</th>
                  <th>Valor</th>
                  <th>Método de Pagamento</th>
                  <th>Status</th>
                  <th className="text-center align-middle">Ações</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  Array(3)
                    .fill(0)
                    .map((_, index) => (
                      <tr key={index} className="animate-pulse">
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </td>
                        <td className="py-4">
                          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        </td>
                        <td className="py-4 text-center">
                          <div className="h-4 w-4 bg-gray-200 rounded-full mx-auto"></div>
                        </td>
                      </tr>
                    ))
                ) : subscriptionHistory.length > 0 ? (
                  currentHistoryItems.map((payment, index) => (
                    <React.Fragment key={index}>
                      <tr className="border-b">
                        <td className="py-2">
                          {[
                            formatCycleDate(payment.currentPeriodStart),
                            formatCycleDate(payment.currentPeriodEnd),
                          ].join(" - ")}
                        </td>
                        <td className="py-2">
                          {payment.items[0].name}
                          {payment.isCurrent && !payment.isUpgrade && (
                            <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-1 rounded-md dark:bg-green-900 dark:text-green-300">
                              Atual
                            </span>
                          )}
                          {payment.isUpgrade && (
                            <span className="ml-2 text-xs font-medium me-2 px-2.5 py-1 rounded-md bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600 text-white">
                              🔥 Upgrade
                            </span>
                          )}
                        </td>
                        <td className="py-2">{formatCurrency(getTotal(payment.items))}</td>
                        <td className="py-2">{t(`payment.${payment.paymentMethod}`)}</td>
                        <td className="py-2 capitalize">{t(`status.${payment.status}`)}</td>
                        <td className="py-2 text-center align-middle">
                          {payment.status !== "future" && (
                            <button
                              onClick={() =>
                                navigate(`/invoices/${payment.id}`, {
                                  state: {
                                    payment,
                                    hasActiveSubscription: subscription?.status === "active"
                                  }
                                })
                              }
                              title="Ver faturas"
                              className={`mx-auto  text-blue-500 hover:opacity-75 focus:outline-none
                                  ${payment.status === "future"
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                                }
                                `}
                              disabled={payment.status === "future"}
                            >
                              <Receipt size={18} />
                            </button>
                          )}
                        </td>
                      </tr>
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td className="py-4 text-center" colSpan={6}>
                      Nenhuma assinatura encontrada
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Controles de Paginação (exibidos apenas se houver mais de uma página) */}
          {subscriptionHistory.length > itemsPerPage && (
            <div className="flex justify-center items-center mt-6 mb-4">
              <nav className="flex items-center gap-4">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1"
                >
                  <ArrowLeftIcon />
                </button>

                <span className="text-sm text-gray-700">
                  {currentPage} / {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-2xl px-3 py-1"
                >
                  <ArrowRightIcon />
                </button>
              </nav>{" "}
            </div>
          )}
        </Card>
      </div>
      <PixPayments
        isOpen={!!paymentData && subscription?.paymentMethod === "pix"}
        payment={{
          pix: {
            amount: paymentData?.amount,
            expiresAt: paymentData?.due_at,
            identifier: paymentData?.identifier,
            qrCode: paymentData?.qr_code,
          }
        }}
        onClose={() => setPaymentData(null)}
      />
      <LiveSupportChat />
    </div>
  );
}
