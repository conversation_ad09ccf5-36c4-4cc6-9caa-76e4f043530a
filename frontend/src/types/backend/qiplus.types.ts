export interface Timestamp {
  _seconds: number;
  _nanoseconds: number;
}

export enum Gateway {
  PAGARME = 'pagarme',
  OPENPIX = 'openpix',
}

// Enums permanecem os mesmos
export enum QISubscriptionStatus {
  ACTIVE = 'active',
  FUTURE = 'future',
  CANCELED = 'canceled',
  PENDING = 'pending',
  OVERDUE = 'overdue',
  TRIAL = 'trial',
  EXPIRED = 'expired',
}

export enum QIScheduledAction {
  CANCEL = 'cancel', // Cancela a assinatura
  EXPIRE = 'expire', // Expira a assinatura
  ACTIVATE = 'activate', // Agenda ativação para upgrade/downgrade
}

export enum QIPaymentMethod {
  CREDIT_CARD = 'credit_card',
  BOLETO = 'boleto',
  PIX = 'pix',
}

export enum QIBillingInterval {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface QIAccountConfig {
  data: {
    contacts_max: number;
    yearly_value: number;
    monthly_value: number;
    contacts_min: number;
  };
  modules: {
    shotx: boolean;
  };
  config: any;
}

// Interfaces para Firestore
export interface QISubscriptionItem {
  id: string;
  name: string;
  type: 'module' | 'addon' | 'plan';
  included: number;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface QIPayment {
  id: string;
  subscriptionId: string;
  amount: number;
  status: string;
  paymentMethod: QIPaymentMethod;
  paidAt?: Timestamp;
  dueDate: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface QISubscription {
  id: string;
  accountId: string;
  planId: string;
  status: QISubscriptionStatus;
  billingInterval: QIBillingInterval;
  billingDay: number;
  scheduledAction?: QIScheduledAction;
  startDate: Timestamp;
  endDate?: Timestamp;
  cycle: number;
  currentPeriodStart: Timestamp;
  currentPeriodEnd: Timestamp;
  canceledAt?: Timestamp;
  trialEnd?: Timestamp;

  // Valores
  currency: string;

  // Método de pagamento
  paymentMethod: QIPaymentMethod;
  installments: number;

  // Relacionamentos (referências do Firestore)
  items: QISubscriptionItem[];

  accountConfig: QIAccountConfig;

  // Metadados
  metadata?: any;

  // Datas de controle
  nextBillingDate: string;
  lastBillingDate?: Timestamp;
  createdAt: number;
  updatedAt: number;

  isUpgrade?: boolean;
  upgradeTo?: string;
  invoices?: QISubscriptionInvoice[];

  gateway: Gateway;
}

export interface QISubscriptionExented extends QISubscription {
  plan: any;
  amount: number;
  next_billing_date: string;
  interval: string;
}

export type QISubscriptionInvoice = {
  id: string;
  chargeId: string;
  transaction_type: string;
  amount: number;
  status: string;
  created_at: Timestamp;
  installment: number;
};

export type QISubscriptionInvoiceBoleto = QISubscriptionInvoice & {
  url: string;
  barcode: string;
  qr_code: string;
  pdf: string;
  due_at: Timestamp;
  line: string;
};

export type QISubscriptionInvoicePix = QISubscriptionInvoice & {
  qr_code: string;
  url: string;
  due_at: Timestamp;
  identifier: string;
};

export type QISubscriptionInvoiceCreditCard = QISubscriptionInvoice & {
  identifier: string;
};
