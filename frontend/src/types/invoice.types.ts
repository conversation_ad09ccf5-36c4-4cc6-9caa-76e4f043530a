/**
 * Interface for Invoice data
 */
export interface Invoice {
  id: string;
  amount: number;
  status: string;
  due_at: string;
  transaction_type: string;
  installment: number;
  metadata?: Record<string, any>;
}

/**
 * Interface for Plan Item data
 */
export interface PlanItem {
  id: string;
  name: string;
  quantity?: number;
  totalPrice: number;
}

/**
 * Interface for Invoice response from API
 */
export interface InvoiceResponse {
  invoices: Invoice[];
  subscription: {
    id: string;
    plan: string;
    nextBillingDate: string;
    amount: number;
    interval: string;
    status: string;
    metadata?: Record<string, any>;
    items: PlanItem[];
  };
}
