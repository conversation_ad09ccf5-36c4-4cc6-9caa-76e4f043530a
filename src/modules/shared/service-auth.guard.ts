import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

/**
 * Guard for service-to-service communication
 * Verifies that the request contains a valid service token
 * The token is defined in the .env file as SERVICE_AUTH_TOKEN
 */
@Injectable()
export class ServiceAuthGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractToken(request);
    const serviceToken = this.configService.get<string>('SERVICE_AUTH_TOKEN');

    if (!serviceToken) {
      Logger.error('SERVICE_AUTH_TOKEN not defined in environment variables');
      throw new UnauthorizedException('Service authentication not configured');
    }

    if (!token) {
      throw new UnauthorizedException('Token not found');
    }

    if (token !== serviceToken) {
      throw new UnauthorizedException('Invalid service token');
    }

    // Token is valid, allow the request
    Logger.log('Service authentication successful');
    return true;
  }

  private extractToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }
}
