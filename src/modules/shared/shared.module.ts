import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ServiceAuthGuard } from './service-auth.guard';

/**
 * SharedModule contém serviços e provedores que são compartilhados entre múltiplos módulos
 * Isso ajuda a evitar dependências circulares
 */
@Global()
@Module({
  imports: [ConfigModule],
  providers: [ServiceAuthGuard],
  exports: [ServiceAuthGuard, ConfigModule],
})
export class SharedModule { }
