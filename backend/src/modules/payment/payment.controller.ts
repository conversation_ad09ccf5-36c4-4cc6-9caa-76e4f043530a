import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentService } from './payment.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import { FirebaseAccountGuard } from '../auth/firebase-account.guard';

@Controller('/payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) { }

  @Post('subscribe')
  @ApiBearerAuth()
  @UseGuards(FirebaseAccountGuard)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  subscribe(@Body() createPaymentDto: CreatePaymentDto, @Req() request: any) {
    return this.paymentService.subscribe(createPaymentDto, request.account);
  }
}
