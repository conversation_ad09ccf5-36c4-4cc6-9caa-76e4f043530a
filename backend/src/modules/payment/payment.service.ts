import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { randomUUID } from 'crypto';
import { AccountService } from 'src/modules/account/account.service';
import { Account } from 'src/modules/account/model/account.model';
import { AffiliateService } from 'src/modules/affiliates/affiliate.service';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { PlanService } from 'src/modules/plan/plan.service';
import { calculateYearlyInstallmentsAmount, createSubscriptionPlan } from 'src/utils/subscription.helper';
import { OrderRequest } from '../pagarme/types/pagarme.order.type';
import { SubscriptionService } from '../subscription/subscription.service';
import { UpgradeService } from '../upgrade/upgrade.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentMethod } from './enum/paymentMethod.enum';
import { GatewayService } from './gateway.service';
import { PaymentGateway } from './types/gateway.types';
import { calculateInstallmentDueDate, formatDataToCreditCard } from './utils/payment.utils';
import { QIBillingInterval, QIPaymentMethod, QISubscription, QISubscriptionItem } from '../subscription/types/qiplus.types';
import { dateAddDays, dateAddYears } from 'src/utils/date';

@Injectable()
export class PaymentService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly accountService: AccountService,
    private readonly planService: PlanService,
    private readonly affiliateService: AffiliateService,
    private readonly upgradeService: UpgradeService,
    private readonly configService: ConfigService,
    private readonly subscriptionService: SubscriptionService,
  ) { }

  async subscribe(createPaymentDto: CreatePaymentDto, account: Account) {
    const { paymentMethod, accountId, isYearly, billingDay } = createPaymentDto;

    const uid = account.owner;
    Logger.log('CONTA OBTIDA ✅');

    // Verifica se é um upgrade (tem assinatura ativa)
    const activeSubscription =
      await this.subscriptionService.getActiveSubscription(accountId);

    if (activeSubscription) {
      Logger.log('ASSINATURA ATIVA ✅');
      return await this.upgradeService.processUpgradeFromSubscription(
        activeSubscription,
        account as Account,
        createPaymentDto,
      );
    }

    const gateway = new GatewayService<typeof paymentMethod>(
      paymentMethod,
      this.configService,
    );
    let customer = await gateway.findCustomerByUid(uid);

    if (!customer) {
      console.log('Criando novo customer');
      const response = await gateway.createCustomer(createPaymentDto);
      customer = response.customer;
    } else {
      console.log('Atualizando customer existente');
      const updateResponse = await gateway.updateCustomer(
        customer.id,
        createPaymentDto,
      );
      if (updateResponse.error) {
        console.log('Erro ao atualizar customer', updateResponse);
        throw new Error(updateResponse.message);
      }

      customer = updateResponse;
    }

    const customerId = customer.id;

    const {
      data: plan,
      error: planError,
      message: planMessage,
    } = await this.planService.getPlanById(createPaymentDto.planId);
    if (planError || !plan) {
      console.error(planMessage);
      throw new Error(planMessage);
    }
    Logger.log('PLANO ENCONTRADO ✅');

    const newSubscription = createSubscriptionPlan({
      plan,
      createPaymentDto,
      account: account as Account,
      customerId,
    });
    Logger.log(newSubscription, 'ASSINATURA PREPARADA ✅');

    if (!newSubscription) {
      console.error('Erro ao preparar dados da assinatura');
      throw new Error('Erro ao preparar dados da assinatura');
    }

    const { error, message, data } =
      await this.subscriptionService.createSubscription(newSubscription);
    if (error) {
      console.log('Erro ao criar assinatura', message);
      throw new Error(message);
    }
    const subscriptionId = data!.id;
    Logger.log(newSubscription, 'ASSINATURA CRIADA ✅');

    // Prepare card if needed
    let cardId = createPaymentDto.cardId;
    if (paymentMethod === PaymentMethod.CREDIT_CARD) {
      if (!cardId) {
        const cardData = formatDataToCreditCard(createPaymentDto, customerId);
        const newCard = await gateway.createCard(cardData);
        cardId = newCard.id;
        if (!cardId) {
          throw new Error('Failed to create card');
        }
      }
    }
    newSubscription.cardId = cardId;

    // Get the number of installments
    let installments = Number(createPaymentDto.installments || '1');

    // Planos mensais sempre serão 1 parcela independente do método
    if (!isYearly) {
      installments = 1;
    }

    // Planos anuais podem ser parcelados em todos os métodos

    // Prepare order data for all installments
    const orderPromises: Promise<any>[] = [];
    const orderDataArray: OrderRequest[] = [];

    // If billing day us different from today, the first installment is due in 3 days
    // And the amount is proportional to the number of days from today to the billing day
    const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
      newSubscription.items,
      installments,
      billingDay,
      0,
      newSubscription.currentPeriodEnd.toDate(),
    );

    // Create order data for each installment
    for (let i = 1; i <= installments; i++) {
      const amount = i === 1 ? firstInstallmentAmount : remainingInstallmentAmount;

      const orderData: OrderRequest = {
        code: `${subscriptionId}-${i}`,
        customer_id: customerId,
        items: [
          {
            amount: Math.round(amount),
            description: `Assinatura ${plan.name} - Parcela ${i}/${installments}`,
            quantity: 1,
          },
        ],
        payments: [],
        metadata: {
          subscriptionId,
          accountId,
          gateway: PaymentGateway[paymentMethod],
          installmentNumber: i,
          totalInstallments: installments
        },
      };

      // Calcular a data de vencimento para esta parcela
      const dueDate = calculateInstallmentDueDate(i, billingDay);

      // Add payment method
      switch (paymentMethod) {
        case PaymentMethod.CREDIT_CARD:
          // Para cartão de crédito, mantemos apenas um pedido com todas as parcelas
          if (i === 1) {
            // Apenas o primeiro pedido para cartão de crédito
            orderData.payments.push({
              payment_method: 'credit_card',
              credit_card: {
                card_id: cardId!,
                installments: installments, // Aqui definimos o número total de parcelas
                statement_descriptor: 'AssinaturaQIPLUS',
              },
            });
          } else {
            // Para cartão de crédito, pulamos a criação de pedidos adicionais
            continue;
          }
          break;
        case PaymentMethod.PIX:
          // Para PIX, criamos um pedido por parcela
          orderData.payments.push({
            payment_method: 'pix',
            pix: {
              expires_at: dueDate, // Data de expiração do QR code
              additional_information: [
                {
                  name: `Assinatura QIPLUS - Parcela ${i}/${installments}`,
                  value: plan.name,
                },
              ],
            },
          });
          break;
        case PaymentMethod.BOLETO:
          // Para Boleto, criamos um pedido por parcela
          const docNumber = randomUUID().replace(/-/g, '').slice(0, 16);
          orderData.payments.push({
            payment_method: 'boleto',
            boleto: {
              instructions: 'Pagar até o vencimento',
              document_number: docNumber,
              type: 'DM',
              due_at: dueDate, // Data de vencimento do boleto
            },
          });
          orderData.metadata['docNumber'] = docNumber;
          break;
        default:
          throw new Error('Método de pagamento não suportado');
      }

      // Store order data
      orderDataArray.push(orderData);
    }

    Logger.log(orderDataArray, 'PEDIDOS PREPARADOS ✅');

    // Create a promise for the first order (we need this result first)
    const firstOrderPromise = gateway.createOrder(orderDataArray[0]);

    // Create promises for the remaining orders (2nd installment and beyond)
    if (installments > 1) {
      for (let i = 1; i < orderDataArray.length; i++) {
        orderPromises.push(gateway.createOrder(orderDataArray[i]));
      }
    }

    // Wait for the first order to complete
    const { error: firstOrderError, data: firstOrderResult } = await firstOrderPromise;
    if (firstOrderError) {
      console.error(`Erro ao criar pedido para parcela 1/${installments}`, firstOrderError);
      throw new Error(firstOrderError);
    }

    // Store the first order result for the response
    const orderResult = firstOrderResult;

    // Process remaining orders in the background
    if (installments > 1 && paymentMethod !== PaymentMethod.CREDIT_CARD) {
      // We don't need to await this, as we already have the first order result
      Promise.all(orderPromises).then(results => {
        const hasError = results.some(result => result.error);
        if (hasError) {
          const errors = results.filter(result => result.error);
          console.error('ERRO AO CRIAR PEDIDOS X', errors);
        } else {
          console.log('PEDIDOS CRIADOS ✅', results);
        }
      }).catch(error => {
        Logger.error(`Erro ao processar pedidos adicionais: ${error.message}`);
      });
    }

    Logger.log(`Iniciado processamento de ${installments} pedidos para assinatura ${subscriptionId} ✅`);

    return {
      error: false,
      message: 'Pedido criado com sucesso',
      data: {
        id: subscriptionId,
        ...newSubscription,
        customer: orderResult.customer,
        card: orderResult.card,
        current_cycle: {
          id: subscriptionId,
          start_at: newSubscription.currentPeriodStart.toDate().toISOString(),
          end_at: newSubscription.currentPeriodEnd?.toDate().toISOString(),
          billing_at: newSubscription.nextBillingDate.toDate().toISOString(),
          status: newSubscription.status,
          cycle: 1,
        },
      },
    };
  }

  async processPayment(subscription: QISubscription): Promise<{
    error: boolean;
    message: string;
    data?: any;
  }> {
    const { id, accountId, cycle = 1, paymentMethod, installments = 1, cardId } = subscription;
    let customerId = subscription.customerId;
    console.log('Processando pagamento para assinatura', id);

    if (paymentMethod === undefined) {
      // console.error('❌ Método de pagamento não suportado');
      return {
        error: true,
        message: 'Método de pagamento não suportado',
      };
    }

    if (paymentMethod === QIPaymentMethod.CREDIT_CARD && !cardId) {
      // console.error('❌ Ignorando pagamento: cartão de crédito sem cartão associado');
      return {
        error: true,
        message: 'Cartão de crédito não associado à assinatura',
      };
    }

    const paymentMethodGTW = paymentMethod as unknown as PaymentMethod;
    const gateway = new GatewayService<typeof paymentMethodGTW>(
      paymentMethodGTW,
      this.configService,
    );

    const account = await this.accountService.getAccount(accountId);
    if (!account) {
      // console.error('❌ Conta não encontrada');
      return {
        error: true,
        message: 'Conta não encontrada',
      };
    }

    const plan = subscription.items.find((item) => item.type === 'plan');

    if (!customerId) {
      const customer = await gateway.findCustomerByUid(account.owner!);
      if (!customer?.id) {
        // console.error(`❌ Customer não encontrado para assinatura ${id}`);
        return {
          error: true,
          message: 'Customer não encontrado',
        };
      }
      customerId = customer?.id;
    }


    // Preparar valores
    const prepareValue = (value: number) => {
      return value % 1 !== 0 ? Math.round(value * 100) : value;
    };

    const orderData: OrderRequest = {
      code: `${id}-${cycle + 1}`,
      customer_id: customerId,
      items: subscription.items
        .filter((item) => item.totalPrice > 0)
        .flatMap((item) => ({
          amount: prepareValue(item.totalPrice),
          description: item.name,
          quantity: item.quantity,
          code: item.id,
        })),
      payments: [],
      metadata: {
        subscriptionId: id,
        accountId,
        gateway: PaymentGateway[paymentMethod],
        installmentNumber: cycle + 1,
        totalInstallments: installments,
        isRenewal: true,
      },
    };

    const isYearly = subscription.billingInterval === QIBillingInterval.YEARLY;
    const expires_at = isYearly ? dateAddYears(new Date(), 1) : dateAddDays(new Date(), 30);

    // Adicionar método de pagamento
    switch (paymentMethod) {
      case QIPaymentMethod.CREDIT_CARD:
        orderData.payments.push({
          payment_method: 'credit_card',
          credit_card: {
            card_id: cardId!,
            installments: installments,
            statement_descriptor: 'AssinaturaQIPLUS',
          },
        });
        break;

      case QIPaymentMethod.PIX:
        orderData.payments.push({
          payment_method: 'pix',
          pix: {
            expires_at: expires_at.toISOString(),
            additional_information: [
              {
                name: `Assinatura QIPLUS - Parcela ${cycle}/${installments}`,
                value: plan?.name || '',
              },
            ],
          },
        });
        break;

      case QIPaymentMethod.BOLETO:
        const docNumber = randomUUID().replace(/-/g, '').slice(0, 16);
        orderData.payments.push({
          payment_method: 'boleto',
          boleto: {
            instructions: 'Pagar até o vencimento',
            document_number: docNumber,
            type: 'DM',
            due_at: expires_at.toISOString(),
          },
        });
        orderData.metadata['docNumber'] = docNumber;
        break;
    }

    Logger.log('🛒 PEDIDO PREPARADO ✅', orderData);

    const { error, message, data } = await gateway.createOrder(orderData);

    if (error) {
      // Logger.error(`❌ Erro ao criar pedido: ${message}`);
      return {
        error: true,
        message,
      };
    }

    Logger.log(`✅ Pedido criado com sucesso para assinatura ${id}`);
    return {
      error: false,
      message: 'Pedido de renovação criado com sucesso',
      data,
    };
  }

}