import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import {
  QIScheduledAction,
  QISubscription,
  QISubscriptionInvoiceBoleto,
  QISubscriptionInvoiceCreditCard,
  QISubscriptionInvoicePix,
  QISubscriptionStatus,
} from 'src/modules/core/types';
import { Account } from '../account/model/account.model';
import { SubscriptionDetails } from '../pagarme/types/subscriptions.details';
import { SubscriptionRepository } from './subscription.repository';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
  ) { }

  async createSubscription(
    data: Omit<QISubscription, 'id'>,
  ): Promise<DefaultResponse<FirebaseFirestore.DocumentReference | null>> {
    return this.subscriptionRepository.create(data, 'sub');
  }

  async getSubscription(id: string): Promise<QISubscription | null> {
    return this.subscriptionRepository.getSubscription(id);
  }

  async getActiveSubscription(
    accountId: string,
  ): Promise<QISubscription | null> {
    return this.subscriptionRepository
      .getSubscriptionsWhere([
        {
          field: 'accountId',
          operator: '==',
          value: accountId,
        },
        {
          field: 'status',
          operator: '==',
          value: QISubscriptionStatus.ACTIVE,
        },
      ])
      .then((subscriptions) =>
        subscriptions.length > 0 ? subscriptions[0] : null,
      );
  }

  async getSubscriptionsByAccountId(
    accountId: string,
  ): Promise<QISubscription[]> {
    return this.subscriptionRepository.getSubscriptionsWhere([
      {
        field: 'accountId',
        operator: '==',
        value: accountId,
      },
    ]);
  }

  async updateSubscriptionStatus(
    subscriptionId: string,
    status: QISubscriptionStatus,
  ): Promise<boolean> {
    return await this.subscriptionRepository
      .update(subscriptionId, { status })
      .then(() => true)
      .catch(() => false);
  }

  /**
   * Agenda o cancelamento da assinatura atual e cria uma nova com o novo plano
   * @param subscriptionId - ID da assinatura atual
   * @param newSubscription - Nova assinatura a ser criada
   * @returns true se a assinatura foi atualizada com sucesso, false caso contrário
   */
  async scheduleUpgradeSubscription(
    subscriptionId: string,
    newSubscription: Omit<QISubscription, 'id'>,
  ): Promise<boolean> {
    await this.subscriptionRepository.update(subscriptionId, {
      scheduledAction: QIScheduledAction.CANCEL,
    });

    await this.createSubscription({
      ...newSubscription,
      accountId: newSubscription.accountId,
      planId: newSubscription.planId,
      status: QISubscriptionStatus.FUTURE,
      scheduledAction: QIScheduledAction.ACTIVATE,
    });
    return true;
  }

  /**
   * Confirma o upgrade ou downgrade da assinatura
   * @param accountId - ID da conta
   * @param subscriptionId - ID da assinatura atual
   */
  async updateSubscription(
    subscriptionId: string,
    data: Partial<QISubscription>,
  ): Promise<boolean> {
    return await this.subscriptionRepository
      .update(subscriptionId, data)
      .then(() => true)
      .catch(() => false);
  }

  /**
   * Agenda o downgrade da assinatura atual e cria uma nova com o novo plano
   * @param subscriptionId - ID da assinatura atual
   * @param newSubscription - Nova assinatura a ser criada
   * @returns true se a assinatura foi atualizada com sucesso, false caso contrário
   */
  async scheduleDowngradeSubscription(
    subscriptionId: string,
    newSubscription: Omit<QISubscription, 'id'>,
  ): Promise<boolean> {
    await this.subscriptionRepository.update(subscriptionId, {
      scheduledAction: QIScheduledAction.CANCEL,
    });

    await this.createSubscription({
      ...newSubscription,
      status: QISubscriptionStatus.FUTURE,
      scheduledAction: QIScheduledAction.ACTIVATE,
    });
    return true;
  }

  getSubscriptionIncrements(accountId: string) {
    return this.subscriptionRepository.getSubscriptionsWhere([
      {
        field: 'accountId',
        operator: '==',
        value: accountId,
      },
    ]);
  }

  async getSubscriptionDetails(
    account: Account,
  ): Promise<SubscriptionDetails> {
    const response: SubscriptionDetails = {
      error: false,
      message: '',
      plan: 'Sem plano',
      status: 'Sem status',
      next_billing_date: '',
      amount: 0,
      interval: 'month',
      increments: [],
      invoices: [],
      subscriptions: [],
      paymentMethod: '',
    };

    // Verifica se a conta existe
    if (!account) {
      response.error = true;
      response.message = 'Conta não encontrada';
      return response;
    }

    // Pega todas as assinaturas do usuário
    const subscriptions =
      await this.subscriptionRepository.getSubscriptionsWhere([
        {
          field: 'accountId',
          operator: '==',
          value: account.id!,
        },
      ]);

    if (subscriptions.length === 0) {
      return response;
    }

    // Ordena as assinaturas da mais nova para a mais antiga
    subscriptions.sort((a, b) => a.createdAt - b.createdAt);

    let currentSubscription = subscriptions.find(
      (subscription) => subscription.status === QISubscriptionStatus.ACTIVE,
    );

    if (!currentSubscription) {
      currentSubscription = subscriptions[subscriptions.length - 1];
    }

    let currentInvoices: any[] = [];
    if (currentSubscription.status === QISubscriptionStatus.PENDING) {
      const { error, invoices } = await this.getSubscriptionInvoices(currentSubscription.id);
      if (error) {
        console.error('Error getting invoices', error);
      } else {
        currentInvoices = invoices;
      }
    }

    response.subscriptions = subscriptions
      .flatMap(
        ({
          id,
          nextBillingDate,
          currentPeriodStart,
          currentPeriodEnd,
          billingInterval,
          ...rest
        }) => {
          const isCurrent = id === currentSubscription?.id && currentSubscription.billingInterval === billingInterval && currentSubscription.status === QISubscriptionStatus.ACTIVE;
          return {
            id,
            nextBillingDate: nextBillingDate.toDate().toISOString(),
            currentPeriodStart: currentPeriodStart.toDate().toISOString(),
            currentPeriodEnd: currentPeriodEnd.toDate().toISOString(),
            ...rest,
            isCurrent,
            billingInterval,
          };
        },
      )
      .sort((a, b) => {
        if (a.isCurrent && !b.isCurrent) return -1;
        if (!a.isCurrent && b.isCurrent) return 1;
        return 0;
      });

    if (!currentSubscription) {
      return response;
    }

    // Se tiver 1 ativa, retornar ativa, senão retornar a mais recente
    const status = currentSubscription.status;

    const totalPrice = currentSubscription.items.reduce(
      (acc, item) => acc + item.totalPrice,
      0,
    );

    return {
      error: false,
      message: '',
      plan: currentSubscription.items[0].name,
      status: status,
      scheduledAction: currentSubscription.scheduledAction,
      next_billing_date: currentSubscription.nextBillingDate
        .toDate()
        .toISOString(),
      amount: totalPrice,
      interval: currentSubscription.billingInterval,
      increments: currentSubscription.items.filter(
        (item) => item.type !== 'plan' && item.totalPrice > 0,
      ),
      invoices: currentInvoices,
      paymentMethod: currentSubscription.paymentMethod,
      subscriptions: response.subscriptions,
    };
  }

  async setSubscriptionInvoice(
    subscriptionId: string,
    invoiceId: string,
    subscriptionInvoice:
      | QISubscriptionInvoiceBoleto
      | QISubscriptionInvoicePix
      | QISubscriptionInvoiceCreditCard,
  ) {
    return this.subscriptionRepository.setSubscriptionInvoice(
      subscriptionId,
      invoiceId,
      subscriptionInvoice,
    );
  }

  async getSubscriptionInvoices(subscriptionId: string, withSubscription = false) {
    const response = { invoices: [] as any[], subscription: null as any, error: false, message: '' };

    if (withSubscription) {
      const subscription =
        await this.subscriptionRepository.getSubscription(subscriptionId);
      if (!subscription) {
        response.error = true;
        response.message = 'Subscription not found';
        return response;
      }

      response.subscription = {
        ...subscription,
        nextBillingDate: subscription.nextBillingDate.toDate().toISOString(),
        currentPeriodStart: subscription.currentPeriodStart
          .toDate()
          .toISOString(),
        currentPeriodEnd: subscription.currentPeriodEnd.toDate().toISOString(),
        plan: subscription.items[0].name,
        interval: subscription.billingInterval,
        items: subscription.items,
      };
    };

    const invoices =
      await this.subscriptionRepository.getSubscriptionInvoices(subscriptionId);

    response.invoices = invoices.docs
      .map((doc) => doc.data())
      .sort(
        (a, b) => a.due_at.toDate().getTime() - b.due_at.toDate().getTime(),
      )
      .flatMap((invoice) => {
        return {
          ...invoice,
          due_at: invoice.due_at?.toDate()?.toISOString() || '', // TODO: verificar onde e porque não salvou a data de vencimento
          id: invoice.id,
        };
      });
    return response;
  }

  async getSubscriptionInvoice(subscriptionId: string, invoiceId: string) {
    return this.subscriptionRepository.getSubscriptionInvoice(
      subscriptionId,
      invoiceId,
    );
  }

  async getSubscriptionPaidInvoices<T>(subscriptionId: string) {
    const invoices =
      await this.subscriptionRepository.getSubscriptionInvoicesWhere(
        subscriptionId,
        [
          {
            field: 'status',
            operator: '==',
            value: 'paid',
          },
        ],
      );
    return invoices.docs.map((doc) => doc.data() as T);
  }

  async scheduleCancelSubscription(subscriptionId: string) {
    return this.subscriptionRepository.update(subscriptionId, {
      scheduledAction: QIScheduledAction.CANCEL,
    });
  }

  async cancelScheduledAction(subscriptionId: string) {
    return this.subscriptionRepository.update(subscriptionId, {
      scheduledAction: null,
    });
  }

  async scheduleActivateSubscription(subscriptionId: string) {
    return this.subscriptionRepository.update(subscriptionId, {
      scheduledAction: QIScheduledAction.ACTIVATE,
    });
  }
}
