import { Injectable } from '@nestjs/common';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { PlanService } from 'src/modules/plan/plan.service';
import { PagarmeService } from '../pagarme/pagarme.service';

@Injectable()
export class SubscriptionsService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly planService: PlanService,
    private readonly pagarmeService: PagarmeService,
  ) {}

  async getSubscriptionIncrements(accountId: string) {
    const accountResponse =
      await this.firebaseService.getAccountByID(accountId);
    if (accountResponse.error) {
      return accountResponse;
    }
    const { data: account } = accountResponse;
    const { payment_status } = account;
    if (!payment_status) {
      return {
        error: false,
        message: '',
        data: [],
      };
    }

    const { gateway } = payment_status;
    const subscription = account[gateway]?.subscription;
    if (!subscription) {
      return {
        error: false,
        message: '',
        data: [],
      };
    }

    const isYearly = subscription.plan.interval === 'year';

    const planResponse = await this.planService.getPlanById(account.planId);
    if (planResponse.error) {
      return planResponse;
    }

    const { data: plan } = planResponse;
    const increments = plan?.customFeatures
      .map(({ id, included, quantity, monthlyPrice, yearlyPrice }) => {
        if (account.config[id]) {
          const extras = Math.max(quantity, account.config[id] - included);
          const price = (isYearly ? yearlyPrice * 12 : monthlyPrice) * extras;
          if (extras > 0) {
            return {
              id,
              included,
              extras,
              price: Math.round((price + Number.EPSILON) * 100) / 100,
            };
          }
        }
        return null;
      })
      .filter((feature) => feature !== null);

    return increments;
  }
}
