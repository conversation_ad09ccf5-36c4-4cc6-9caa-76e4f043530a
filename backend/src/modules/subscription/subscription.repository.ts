import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import {
  QISubscription,
  QISubscriptionInvoice,
} from 'src/modules/core/types';
import { ResponseUtil } from 'src/utils/response';

@Injectable()
export class SubscriptionRepository {
  private firestore: FirebaseFirestore.Firestore;
  private collection: FirebaseFirestore.CollectionReference;

  constructor(private readonly firebaseService: FirebaseService) {
    this.firestore = this.firebaseService.getFirestore();
    this.collection = this.firestore.collection('subscriptions');
  }

  async create(
    subscription: Omit<QISubscription, 'id'>,
    prefix?: string,
  ): Promise<DefaultResponse<admin.firestore.DocumentReference | null>> {
    let docId = this.collection.doc().id;
    if (prefix) {
      docId = `${prefix}_${docId}`;
    }
    const docRef = this.collection.doc(docId);
    return await docRef
      .set({
        ...subscription,
        id: docId,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      })
      .then(() => ResponseUtil.success('Subscription created', docRef))
      .catch((error) => ResponseUtil.error(error.message));
  }

  async getSubscription(
    subscriptionId: string,
  ): Promise<QISubscription | null> {
    const doc = await this.collection.doc(subscriptionId).get();
    return doc.data() as QISubscription | null;
  }

  async getSubscriptionsWhere(
    where: {
      field: string;
      operator: FirebaseFirestore.WhereFilterOp;
      value: string;
    }[],
  ): Promise<QISubscription[]> {
    let query: admin.firestore.Query = this.collection;
    where.forEach(({ field, operator, value }) => {
      query = query.where(field, operator, value);
    });
    const docs = await query.get();
    return docs.docs.map((doc) => doc.data() as QISubscription);
  }

  async update(id: string, data: any): Promise<FirebaseFirestore.WriteResult> {
    return await this.collection.doc(id).update(
      {
        ...data,
        updatedAt: admin.firestore.Timestamp.now(),
      },
      { merge: true },
    );
  }

  async setSubscriptionInvoice(
    subscriptionId: string,
    invoiceId: string,
    subscriptionInvoice: QISubscriptionInvoice,
  ) {
    return await this.collection
      .doc(subscriptionId)
      .collection('invoices')
      .doc(invoiceId)
      .set(subscriptionInvoice);
  }

  async getSubscriptionInvoice(subscriptionId: string, invoiceId: string) {
    return await this.collection
      .doc(subscriptionId)
      .collection('invoices')
      .doc(invoiceId)
      .get();
  }

  async getSubscriptionInvoices(subscriptionId: string) {
    return await this.collection
      .doc(subscriptionId)
      .collection('invoices')
      .get();
  }

  async getSubscriptionInvoicesWhere(
    subscriptionId: string,
    where: {
      field: string;
      operator: FirebaseFirestore.WhereFilterOp;
      value: string;
    }[],
  ) {
    let query: admin.firestore.Query = this.collection
      .doc(subscriptionId)
      .collection('invoices');
    where.forEach(({ field, operator, value }) => {
      query = query.where(field, operator, value);
    });
    return await query.get();
  }
}
