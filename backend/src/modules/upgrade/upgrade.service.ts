import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { randomUUID } from 'crypto';
import { AccountService } from 'src/modules/account/account.service';
import { Account } from 'src/modules/account/model/account.model';
import { OrderRequest } from 'src/modules/pagarme/types/pagarme.order.type';
import { PaymentSubscriptionResponse } from 'src/modules/core/types';
import { PlanService } from 'src/modules/plan/plan.service';
import {
  createSubscriptionPlan,
} from 'src/utils/subscription.helper';
import { CreatePaymentDto } from '../payment/dto/create-payment.dto';
import { PaymentMethod } from '../payment/enum/paymentMethod.enum';
import { GatewayService } from '../payment/gateway.service';
import { PaymentGateway } from '../payment/types/gateway.types';
import {
  calculateInstallmentDueDate,
  formatDataToCreditCard,
} from '../payment/utils/payment.utils';
import {
  UpgradeCalculatorFromSubscription,
} from '../payment/utils/upgrade-calculator';
import { SubscriptionService } from '../subscription/subscription.service';
import {
  QIBillingInterval,
  QIScheduledAction,
  QISubscription,
  QISubscriptionInvoice,
  QISubscriptionStatus,
} from 'src/modules/core/types';
import { PlanPreviewDto } from './dto/upgrade-preview.dto';
@Injectable()
export class UpgradeService {
  constructor(
    private readonly accountService: AccountService,
    private readonly upgradeCalculatorFromSubscription: UpgradeCalculatorFromSubscription,
    private readonly planService: PlanService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    private readonly configService: ConfigService,
  ) { }

  log(message: string) {
    Logger.log(message, 'UPGRADE SERVICE');
  }

  /**
   * Preview upgrade
   * @param currentSubscription
   * @param account
   * @param body
   * @returns a list of plans with the calculated values of the upgrade
   */
  async plansPreview(
    currentSubscription: QISubscription,
    account: Account,
    body: PlanPreviewDto,
  ): Promise<any> {
    // Get all plans
    try {
      const { error, data: plans } = await this.planService.getPlans();
      if (error || !plans) {
        throw new Error('Error getting plans');
      }

      const invoices =
        await this.subscriptionService.getSubscriptionPaidInvoices<QISubscriptionInvoice>(
          currentSubscription.id,
        );

      this.log('INVOICES ENCONTRADAS ✅');
      const amountPaid = invoices.reduce(
        (acc, invoice) => acc + invoice.amount,
        0,
      );
      if (amountPaid === 0) {
        this.log('NENHUMA FATURA PAGA ENCONTRADA ❌');
        return [
          ...plans.flatMap((plan: any) => ({
            ...plan,
            uniqueId: `${plan.id}_yearly`,
            isYearly: true,
          })),
          ...plans.flatMap((plan: any) => ({
            ...plan,
            uniqueId: `${plan.id}_monthly`,
            isYearly: false,
          }))
        ];
      }

      this.log('VALOR TOTAL PAGO IN CENTS: ' + amountPaid);

      let credit =
        this.upgradeCalculatorFromSubscription.calculateSubscriptionCredit(
          currentSubscription,
          amountPaid,
        );

      this.log('CREDITO CALCULADO: ' + credit);

      const isCurrentYearly = currentSubscription.billingInterval === QIBillingInterval.YEARLY;

      const calculatedPlans = plans.map((plan) => {
        const isCurrentPlan = plan.id === currentSubscription.planId;

        delete plan.planData['keywords'];
        delete plan.planData['actions'];
        delete plan.planData['pagarme'];
        delete plan.planData['logs'];
        return {
          ...plan.planData,
          isCurrentPlan,
        };
      });

      return [
        ...calculatedPlans.flatMap((plan: any) => ({
          ...plan,
          uniqueId: `${plan.id}_yearly`,
          isYearly: true,
          credit: !isCurrentYearly ? 0 : credit,
        })),
        ...calculatedPlans.flatMap((plan: any) => ({
          ...plan,
          uniqueId: `${plan.id}_monthly`,
          isYearly: false,
          credit: isCurrentYearly ? 0 : credit,
        }))
      ];
    } catch (error) {
      console.error('Error processing upgrade:', error);
      throw error;
    }
  }

  async processUpgradeFromSubscription(
    currentSubscription: QISubscription,
    account: Account,
    createPaymentDto: CreatePaymentDto,
  ): Promise<PaymentSubscriptionResponse> {
    const response: PaymentSubscriptionResponse = {
      error: true,
      message: '',
      errors: [],
      isUpgrade: true,
      skipPayment: false,
    };
    try {
      // Buscar o plano novo
      const { data: newPlan, error: newPlanError } =
        await this.planService.getPlanById(createPaymentDto.planId);
      if (newPlanError || !newPlan) {
        response.message = 'Plano novo não encontrado';
        return response;
      }
      this.log('PLANO NOVO ENCONTRADO ✅');

      // Verificar se está tentando mudar de plano mensal para anual
      const isChangingBillingCycle = currentSubscription.billingInterval === QIBillingInterval.YEARLY !== createPaymentDto.isYearly

      // Atualizar o plano novo com os dados da nova assinatura
      newPlan.updateFromCreatePaymentDto(createPaymentDto);
      this.log('PLANO NOVO ATUALIZADO ✅');

      // Criar nova assinatura
      const newSubscription = createSubscriptionPlan({
        plan: newPlan,
        createPaymentDto,
        account,
        status: QISubscriptionStatus.FUTURE,
        customerId: currentSubscription.customerId,
        cycle: {
          // Se for uma mudança de mensal para anual, a nova assinatura só inicia quando a atual termina
          startDate: isChangingBillingCycle ? currentSubscription.currentPeriodEnd.toDate() : new Date(),
          // Se for uma mudança de mensal para anual, a nova assinatura terá a data de termino calculada
          endDate: !isChangingBillingCycle ? currentSubscription.currentPeriodEnd.toDate() : null,
        },
      });

      if (!newSubscription) {
        throw new Error('Erro ao criar nova assinatura');
      }

      newSubscription.isUpgrade = true;
      const { error, message, data } =
        await this.subscriptionService.createSubscription(newSubscription);
      if (error) {
        this.log('Erro ao criar assinatura ❌');
        console.error(message);
        throw new Error(message);
      }
      const subscriptionId = data!.id;

      // TODO: Vou verificar se isso já está sendo feito no webhook
      // Atualizar a assinatura atual com o id da nova assinatura
      await this.subscriptionService.updateSubscription(
        currentSubscription.id,
        {
          upgradeTo: subscriptionId,
          scheduledAction: QIScheduledAction.EXPIRE,
        },
      );

      const totalNewPlan = newSubscription.items.reduce(
        (acc, item) => acc + item.totalPrice,
        0,
      );
      this.log('VALOR TOTAL DO NOVO PLANO: ' + totalNewPlan);

      let upgradeCredit = 0;
      let remainingAmount = 0;
      // Se não for mudança de ciclo de faturamento, calcular o valor da primeira parcela
      if (!isChangingBillingCycle) {
        const invoices =
          await this.subscriptionService.getSubscriptionPaidInvoices<QISubscriptionInvoice>(
            currentSubscription.id,
          );

        this.log('INVOICES ENCONTRADAS ✅');
        const amountPaid = invoices.reduce(
          (acc, invoice) => acc + invoice.amount + (invoice.upgradeCredit || 0),
          0,
        );

        this.log('VALOR TOTAL PAGO IN CENTS: ' + amountPaid);

        upgradeCredit =
          this.upgradeCalculatorFromSubscription.calculateSubscriptionCredit(
            currentSubscription,
            amountPaid,
          );
        this.log('CREDITO CALCULADO: ' + upgradeCredit);

        remainingAmount = Math.round(totalNewPlan - upgradeCredit);
      }

      const { paymentMethod } = createPaymentDto;
      const gateway = new GatewayService<typeof paymentMethod>(
        paymentMethod,
        this.configService,
      );
      let customer = await gateway.findCustomerByUid(account.owner!);

      if (!customer) {
        this.log('Criando novo customer');
        const response = await gateway.createCustomer(createPaymentDto);
        customer = response.customer;
      } else {
        this.log('Atualizando customer existente ✅');
        const updateResponse = await gateway.updateCustomer(
          customer.id,
          createPaymentDto,
        );
        if (updateResponse.error) {
          this.log('Erro ao atualizar customer ❌');
          console.error(updateResponse.message);
          throw new Error(updateResponse.message);
        }

        customer = updateResponse;
      }

      // Check PIX payment method requirements first
      if (paymentMethod === PaymentMethod.PIX) {
        if (!Object.keys(customer.phones).length) {
          return {
            error: true,
            message: 'Número de telefone não encontrado',
            errors: [],
          };
        }
      }

      let cardId = createPaymentDto.cardId;
      let card = null;
      if (paymentMethod === PaymentMethod.CREDIT_CARD) {
        if (!cardId) {
          this.log('Criando novo cartão ✅');
          const cardData = formatDataToCreditCard(
            createPaymentDto,
            customer.id,
          );
          const cardResponse = await gateway.createCard(cardData);
          card = cardResponse;
          cardId = cardResponse.id;
          if (!cardId) {
            throw new Error('Failed to create card');
          }
          this.log('CARTÃO CRIADO ✅');
        }
      }
      newSubscription.cardId = cardId;

      // Entra aqui os casos de downgrade (mesmo que seja um upgrade mas o valor do crédito é maior que o valor do novo plano) e mudança de ciclo de faturamento
      this.log('VALOR RESTANTE A SER PAGO: ' + remainingAmount);
      // TODO: verificar esse ponto pois aparentemente está errado em atualizar o status como pago pois a cobrança deveria ser gerada somente na data de fim da assinatura atual
      if (remainingAmount <= 0) {
        if (isChangingBillingCycle) {
          this.log('Mudança de ciclo de faturamento detectada ✅');
        } else {
          this.log('Downgrade detectado ✅');
        }
        if (cardId && !card && paymentMethod === PaymentMethod.CREDIT_CARD) {
          this.log('Buscando cartão ✅');
          const cardResponse = await gateway.getCard(customer.id, cardId);
          card = cardResponse;
          this.log('Cartão encontrado ✅');
        }

        this.log('Atualizando conta ✅');
        await this.accountService.updateAccount(account.id!, {
          payment_upgrade_status: {
            error_message: '',
            gateway: PaymentGateway[paymentMethod],
            planId: newPlan.id,
            // Isso é necessário porque a cobrança será gerada somente na data de fim da assinatura atual
            status: isChangingBillingCycle ? 'pending_payment' : 'paid',
            subscription_id: subscriptionId,
          },
        });

        this.log('Agendando expiração da assinatura atual ✅');
        await this.subscriptionService.updateSubscription(
          currentSubscription.id,
          {
            scheduledAction: QIScheduledAction.EXPIRE,
          },
        );

        this.log('Agendando ativação da nova assinatura ✅');
        await this.subscriptionService.updateSubscription(subscriptionId, {
          scheduledAction: QIScheduledAction.ACTIVATE,
        });

        this.log('Retornando resposta ✅');
        return {
          error: false,
          message: isChangingBillingCycle ? 'Assinatura agendada com sucesso' : 'Assinatura atualizada com sucesso',
          isUpgrade: true,
          skipPayment: isChangingBillingCycle,
          data: {
            id: subscriptionId,
            ...newSubscription,
            customer: customer,
            card: card,
            current_cycle: {
              id: subscriptionId,
              start_at: newSubscription.currentPeriodStart
                .toDate()
                .toISOString(),
              end_at: newSubscription.currentPeriodEnd?.toDate().toISOString(),
              billing_at: newSubscription.nextBillingDate
                .toDate()
                .toISOString(),
              status: newSubscription.status,
              cycle: 1,
            },
          },
          errors: [],
        };
      }

      // No upgrade imediato, o número de parcelas da nova assinatura deve ser o número remanescente de parcelas da assinatura atual
      let remainingInstallments =
        currentSubscription.installments - currentSubscription.cycle;

      if (createPaymentDto.isYearly) {
        // Para planos anuais, calculamos as parcelas restantes da assinatura atual
        if (remainingInstallments > 0) {
          this.log(
            `Usando ${remainingInstallments} parcelas restantes da assinatura atual ✅`,
          );
        } else {
          // Se não houver parcelas restantes, então criamos com apenas 1 parcela
          remainingInstallments = 1;
          this.log(
            `Não há parcelas restantes, usando ${remainingInstallments} parcelas informadas pelo usuário ✅`,
          );
        }
      } else {
        // Planos mensais sempre serão 1 parcela independente do método
        remainingInstallments = 1;
        this.log('Plano mensal, usando 1 parcela ✅');
      }

      // Prepare order data for all installments
      const orderPromises: Promise<any>[] = [];
      const orderDataArray: OrderRequest[] = [];

      // Valor de cada parcela
      const installmentAmount = Math.round(remainingAmount / remainingInstallments);
      this.log('VALOR DA PARCELA: ' + installmentAmount);
      this.log('VALOR DO CREDITO: ' + upgradeCredit);

      // Create order data for each installment
      for (let i = 1; i <= remainingInstallments; i++) {

        const orderData: OrderRequest = {
          code: `${subscriptionId}-${i}`,
          customer_id: customer.id,
          items: [
            {
              amount: installmentAmount,
              description:
                i === 1
                  ? `Upgrade ${newPlan.name}`
                  : `Assinatura ${newPlan.name}`,
              quantity: 1,
            },
          ],
          payments: [],
          metadata: {
            subscriptionId,
            accountId: account.id!,
            gateway: PaymentGateway[paymentMethod],
            isUpgrade: true,
            installmentNumber: i,
            totalInstallments: remainingInstallments,
            isFirstPayment: i === 1,
            upgradeCredit,
          },
        };

        // Calcular a data de vencimento para esta parcela
        const dueDate = calculateInstallmentDueDate(i, currentSubscription.billingDay);

        switch (paymentMethod) {
          case PaymentMethod.CREDIT_CARD:
            // Para cartão de crédito, mantemos apenas um pedido com todas as parcelas
            if (i === 1) {
              // Apenas o primeiro pedido para cartão de crédito
              orderData.payments.push({
                payment_method: 'credit_card',
                credit_card: {
                  card_id: createPaymentDto.cardId!,
                  installments: remainingInstallments, // Aqui definimos o número total de parcelas
                  statement_descriptor: 'QIPLUS Upgrade',
                },
              });
            } else {
              // Para cartão de crédito, pulamos a criação de pedidos adicionais
              continue;
            }
            break;
          case PaymentMethod.PIX:
            // Para PIX, criamos um pedido por parcela
            orderData.payments.push({
              payment_method: 'pix',
              pix: {
                expires_at: dueDate, // Data de expiração do QR code
                additional_information: [
                  {
                    name:
                      i === 1
                        ? `Upgrade QIPLUS - Parcela ${i}/${remainingInstallments} (Diferença)`
                        : `Assinatura QIPLUS - Parcela ${i}/${remainingInstallments}`,
                    value: newPlan.name,
                  },
                ],
              },
            });
            break;
          case PaymentMethod.BOLETO:
            // Para Boleto, criamos um pedido por parcela
            const docNumber = randomUUID().replace(/-/g, '').slice(0, 16);
            orderData.payments.push({
              payment_method: 'boleto',
              boleto: {
                instructions: 'Pagar até o vencimento',
                document_number: docNumber,
                type: 'DM',
                due_at: dueDate, // Data de vencimento do boleto
              },
            });
            orderData.metadata['docNumber'] = docNumber;
            break;
          default:
            throw new Error('Método de pagamento não suportado');
        }

        // Store order data
        orderDataArray.push(orderData);
      }

      // Create a promise for the first order (we need this result first)
      const firstOrderPromise = gateway.createOrder(orderDataArray[0]);

      // Create promises for the remaining orders (2nd installment and beyond)
      if (remainingInstallments > 1 && paymentMethod !== PaymentMethod.CREDIT_CARD) {
        // Para cartão de crédito, não criamos múltiplos pedidos
        for (let i = 1; i < orderDataArray.length; i++) {
          orderPromises.push(gateway.createOrder(orderDataArray[i]));
        }
      }

      // Wait for the first order to complete
      const { error: firstOrderError, data: firstOrderResult } =
        await firstOrderPromise;
      if (firstOrderError) {
        console.error(
          `Erro ao criar pedido para parcela 1/${remainingInstallments}`,
          firstOrderError,
        );
        throw new Error(firstOrderError);
      }

      // Store the first order result for the response
      const orderResult = firstOrderResult;

      // Process remaining orders in the background
      if (remainingInstallments > 1 && paymentMethod !== PaymentMethod.CREDIT_CARD) {
        // We don't need to await this, as we already have the first order result
        Promise.all(orderPromises)
          .then((results) => {
            const hasError = results.some((result) => result.error);
            if (hasError) {
              const errors = results.filter((result) => result.error);
              console.error('ERRO AO CRIAR PEDIDOS X', errors);
            } else {
              console.log('PEDIDOS CRIADOS ✅', results);
            }
          })
          .catch((error) => {
            Logger.error(
              `Erro ao processar pedidos adicionais: ${error.message}`,
            );
          });
      }

      this.log(
        `Iniciado processamento de ${remainingInstallments} pedidos para upgrade ${subscriptionId} ✅`,
      );

      return {
        error: false,
        message: 'Upgrade realizado com sucesso',
        data: {
          id: subscriptionId,
          ...newSubscription,
          customer: orderResult.customer,
          card: orderResult.card,
          current_cycle: {
            id: subscriptionId,
            start_at: newSubscription.currentPeriodStart.toDate().toISOString(),
            end_at: newSubscription.currentPeriodEnd?.toDate().toISOString(),
            billing_at: newSubscription.nextBillingDate.toDate().toISOString(),
            status: newSubscription.status,
            cycle: 1,
          },
        },
        errors: [],
      };
    } catch (error) {
      console.error('Error processing upgrade:', error);
      throw error;
    }
  }

  /**
   * Get all available plans without calculations
   * @returns List of plans from database
   */
  async getPlans() {
    const { error, data: plans } = await this.planService.getPlans();
    if (error || !plans) {
      throw new Error('Error getting plans');
    }

    const sanitizedPlans = plans.map((plan) => {
      // Remove sensitive/unnecessary data
      delete plan.planData['keywords'];
      delete plan.planData['actions'];
      delete plan.planData['pagarme'];
      delete plan.planData['logs'];
      return plan.planData;
    });

    return [
      ...sanitizedPlans.flatMap((plan: any) => ({
        ...plan,
        uniqueId: `${plan.id}_yearly`,
        isYearly: true,
      })),
      ...sanitizedPlans.flatMap((plan: any) => ({
        ...plan,
        uniqueId: `${plan.id}_monthly`,
        isYearly: false,
      }))
    ]
  }
}
