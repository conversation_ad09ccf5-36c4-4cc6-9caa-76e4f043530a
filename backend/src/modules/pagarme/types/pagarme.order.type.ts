export type OrderCardId = {
  installments: number;
  statement_descriptor: string;
  card_id: string;
};

export type OrderBoleto = {
  instructions: string;
  due_at?: string;
  document_number: string;
  type: 'DM' | 'BDP';
};

export type OrderPix = {
  expires_in?: number;
  expires_at?: string;
  additional_information: [
    {
      name: string;
      value: string;
    },
  ];
};

export type OrderCard = {
  number: string;
  holder_name: string;
  exp_month: number;
  exp_year: number;
  cvv: string;
  billing_address: {
    line_1: string;
    zip_code: string;
    city: string;
    state: string;
    country: string;
  };
};

export type OrderBillingAddress = {
  zip_code: string;
  city: string;
  state: string;
  country: string;
  line_1: string;
};

export type OrderResponseItem = {
  id: string;
  type: string;
  description: string;
  amount: number;
  quantity: number;
  status: string;
  created_at: string;
  updated_at: string;
  code: string;
};

export type OrderCustomer = {
  id: string;
  name: string;
  email: string;
  document: string;
  document_type: string;
  type: string;
  delinquent: boolean;
  created_at: string;
  updated_at: string;
  phones: Record<string, unknown>;
};

export type OrderShipping = {
  amount: number;
  description: string;
  recipient_name: string;
  recipient_phone: string;
  address: OrderBillingAddress;
};

export type OrderLocation = {
  latitude: string;
  longitude: string;
};

export type OrderRequestPayment = {
  payment_method: 'credit_card' | 'boleto' | 'pix';
  credit_card?: OrderCardId | OrderCard;
  boleto?: OrderBoleto;
  pix?: OrderPix;
};

export type OrderResponse = {
  error: boolean;
  errors: any[];
  message: string;
  data: {
    id: string;
    code: string;
    amount: number;
    currency: 'BRL';
    closed: boolean;
    items: OrderResponseItem[];
    customer: OrderCustomer;
    shipping: OrderShipping;
    status: string;
    created_at: string;
    updated_at: string;
    closed_at: string;
    location: OrderLocation;
    charges: [
      {
        id: string;
        code: string;
        amount: number;
        paid_amount: number;
        status: string;
        currency: string;
        payment_method: string;
        paid_at: string;
        created_at: string;
        updated_at: string;
        customer: {
          id: string;
          name: string;
          email: string;
          document: string;
          document_type: string;
          type: string;
          delinquent: boolean;
          created_at: string;
          updated_at: string;
          phones: Record<string, unknown>;
        };
        last_transaction: {
          operation_key: string;
          id: string;
          transaction_type: string;
          gateway_id: string;
          amount: number;
          status: string;
          success: boolean;
          installments: number;
          statement_descriptor: string;
          acquirer_name: string;
          acquirer_tid: string;
          acquirer_nsu: string;
          acquirer_auth_code: string;
          acquirer_message: string;
          acquirer_return_code: string;
          operation_type: string;
          card: {
            id: string;
            first_six_digits: string;
            last_four_digits: string;
            brand: string;
            holder_name: string;
            exp_month: number;
            exp_year: number;
            status: string;
            type: string;
            created_at: string;
            updated_at: string;
            billing_address: OrderBillingAddress;
          };
          payment_type: string;
          created_at: string;
          updated_at: string;
          gateway_response: {
            code: string;
            errors: [];
          };
          antifraud_response: {};
          metadata: {};
        };
        metadata: Record<string, unknown>;
      },
    ];
    checkouts: [];
    metadata: Record<string, unknown>;
  };
};

export type OrderRequestItem = {
  amount: number;
  description: string;
  quantity: number;
  code?: string;
};

export type OrderRequestCustomer = {
  name: string;
  type: 'individual' | 'company';
  email: string;
  document: string;
  document_type: 'CPF' | 'CNPJ' | 'PASSPORT';
  code?: string;
  gender?: 'male' | 'female';
};

export type OrderRequest = {
  code?: string;
  customer?: OrderRequestCustomer;
  customer_id?: string;
  items: OrderRequestItem[];
  payments: OrderRequestPayment[];
  metadata: Record<string, unknown>;
};
