import { Subscription } from 'src/modules/webhook/types/payment_failed.type';

type SubscriptionWithDetails = Subscription & {
  isCurrent: boolean;
  canceledAt: string | null;
  metadata: Record<string, unknown>;
  status: string;
};

export type SubscriptionDetails = {
  error: boolean;
  message: string;
  plan: string;
  status: string;
  scheduledAction?: string;
  next_billing_date: string;
  amount: number;
  interval: string;
  increments: any[];
  invoices: any[];
  paymentMethod: string;
  subscriptions: any[];
};
