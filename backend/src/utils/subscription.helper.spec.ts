import { QISubscriptionItem } from 'src/modules/core/types';
import { MONTHS } from './date';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from './subscription.helper';

describe('Subscription Helper', () => {
  describe('calculateYearlyBillingDate', () => {
    it('should return the next month if difference between current date and next billing date is less than 30 days', () => {
      const billingDay = 15;

      // jan 10, 2025
      const currentDate = new Date(2025, MONTHS.JAN, 1, 0, 0, 0, 0);
      // fev 15, 2025
      const expectedDate = new Date(2025, MONTHS.FEB, 15, 0, 0, 0, 0);

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
    it('should return the month after next if difference between current date and next billing date is greater than 30 days', () => {
      const billingDay = 5;

      // jan 10, 2025
      const currentDate = new Date(2025, 0, 10, 0, 0, 0, 0);
      // mar 10, 2025
      const expectedDate = new Date(2025, 2, 5, 0, 0, 0, 0);

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
    it('should return the next month if difference between current date and next billing date is less than 30 days and the billing day is the same as the current date', () => {
      const billingDay = 10;

      // jan 10, 2025
      const currentDate = new Date(2025, 0, 10, 0, 0, 0, 0);
      // fev 10, 2025
      const expectedDate = new Date(2025, 1, 10, 0, 0, 0, 0);

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
    it('should return the next month if difference between current date and next billing date is greater than 30 days and the billing year is greater than the current year', () => {
      const billingDay = 10;

      // dec 25, 2024
      const currentDate = new Date(2024, 11, 25, 0, 0, 0, 0);
      // fev 10, 2025
      const expectedDate = new Date(2025, 1, 10, 0, 0, 0, 0);

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
  });

  describe('calculateFirstInstallmentAmount', () => {
    it('should calculate the first installment amount correctly when billing day is in the future', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 15;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        0,
        new Date(2026, MONTHS.JAN, 1, 0, 0, 0, 0),
        new Date(2025, MONTHS.JAN, 1, 0, 0, 0, 0),
      );

      expect(firstInstallmentAmount).toBe(38);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('should calculate the first installment amount correctly when billing day is in the past', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 5;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        0,
        new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0),
        new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0),
      );

      expect(firstInstallmentAmount).toBe(100);
      expect(remainingInstallmentAmount).toBe(100);
    });
  });
});

