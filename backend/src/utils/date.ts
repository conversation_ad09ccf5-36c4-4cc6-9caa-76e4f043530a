export function formatDate(oldDate: Date): string {
    const date = new Date(oldDate)
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const hours = date.getHours()
    const minutes = date.getMinutes()
    return `${day}/${month}/${year} ${hours}:${minutes < 10 ? '0' : ''}${minutes}`;
}

// Helper for date manipulation
export function dateAddDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
}

export function dateAddMonths(date: Date, months: number): Date {
    const result = new Date(date);
    const currentDate = result.getDate();

    result.setMonth(result.getMonth() + months);

    // Ajuste para meses com menos dias (ex: de 31 para fevereiro)
    if (result.getDate() < currentDate) {
        result.setDate(0); // Vai para o último dia do mês anterior
    }

    return result;
}

export function dateAddYears(date: Date, years: number): Date {
    const result = new Date(date);
    result.setFullYear(result.getFullYear() + years);
    return result;
}

export function dateAddHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setHours(result.getHours() + hours);
    return result;
}

export function dateAddMinutes(date: Date, minutes: number): Date {
    const result = new Date(date);
    result.setMinutes(result.getMinutes() + minutes);
    return result;
}

export function dateAddSeconds(date: Date, seconds: number): Date {
    const result = new Date(date);
    result.setSeconds(result.getSeconds() + seconds);
    return result;
}

export const MONTHS = {
    JAN: 0,
    FEB: 1,
    MAR: 2,
    APR: 3,
    MAY: 4,
    JUN: 5,
    JUL: 6,
    AUG: 7,
    SEP: 8,
    OCT: 9,
    NOV: 10,
    DEC: 11,
};