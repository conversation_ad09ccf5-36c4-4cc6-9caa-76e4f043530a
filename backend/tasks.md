Atividades restantes do checkout:

- Aplicar o desconto no total do novo plano e não na 1 parcela - em andamento
- Adicionar uma badge ao planCard para exibir quando houver personalização do plano, algo como:
  '+R$1.000,00 de adicionais personalizados'
- Ajustar os vencimentos das faturas para seguir a mesma regra da data de cobrança - em andamento
- Ajustar o card do item para exibir a quantidade, a descrição e o valor de forma mais elegante
- Adicionar o numero de parcelas ao histórico de assinaturas
- Verificar se o valor cobrado no upgrade está correto - em andamento
- Corrigir no webhook para não atualizar o qr_code com os pedidos subsequentes quando o pagamento é parcelado
- Sugerir o próximo plano caso o valor do atual com customização seja maior
- DEFINIÇÂO:
-     Upgrade com desconto no total
-     Dowgrade financeiro, aplica o upgrade e não cobra até consumir o crédito
- Nos casos em que o usuário pode selecionar a data de cobrança da fatura, devemos calcular o valor da entrada proporcional ao dia que ele está assinando até o dia escolhido por ele no mês seguinte
